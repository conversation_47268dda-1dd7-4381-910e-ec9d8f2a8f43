/* TodoManagement 现代化样式 */

/* 主容器 */
.todoContainer {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.6s ease-out;
}

.todoContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

/* 装饰元素 */
.decorativeElement {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  animation: float 6s ease-in-out infinite;
}

.decorativeElement:nth-child(1) {
  top: -50px;
  right: -50px;
  animation-delay: 0s;
}

.decorativeElement:nth-child(2) {
  bottom: -30px;
  left: -30px;
  animation-delay: 2s;
  width: 60px;
  height: 60px;
}

/* 标题样式 */
.todoTitle {
  color: white !important;
  font-size: 24px !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.todoSubtitle {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 14px !important;
  margin-bottom: 24px !important;
}

/* 统计卡片 */
.statsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.statCard {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.statCard:hover::before {
  left: 100%;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.statValue {
  font-size: 28px;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 4px;
}

.statLabel {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 进度条样式 */
.progressContainer {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.progressLabel {
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

/* 操作区域 */
.actionContainer {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.searchInput {
  border-radius: 8px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
}

.searchInput input {
  background: transparent !important;
  color: white !important;
}

.searchInput input::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

.addButton {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3) !important;
  transition: all 0.3s ease !important;
}

.addButton:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4) !important;
}

/* 标签页样式 */
.todoTabs {
  margin-bottom: 16px;
}

.todoTabs .ant-tabs-tab {
  color: rgba(255, 255, 255, 0.7) !important;
  font-weight: 500;
}

.todoTabs .ant-tabs-tab-active {
  color: white !important;
}

.todoTabs .ant-tabs-ink-bar {
  background: white !important;
}

/* 任务列表容器 */
.todoListContainer {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 400px;
}

/* 任务卡片 */
.todoItem {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: slideInUp 0.3s ease-out;
}

.todoItem::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: all 0.3s ease;
}

.todoItem.highPriority::before {
  background: linear-gradient(180deg, #ff4d4f 0%, #ff7875 100%);
}

.todoItem.mediumPriority::before {
  background: linear-gradient(180deg, #faad14 0%, #ffc53d 100%);
}

.todoItem.lowPriority::before {
  background: linear-gradient(180deg, #8c8c8c 0%, #bfbfbf 100%);
}

.todoItem.completed::before {
  background: linear-gradient(180deg, #52c41a 0%, #73d13d 100%);
}

.todoItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.todoItem.completed {
  opacity: 0.7;
}

/* 任务状态指示器 */
.statusIndicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.statusIndicator.completed {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}

.statusIndicator.pending {
  border: 2px solid;
  background: white;
}

.statusIndicator.pending.highPriority {
  border-color: #ff4d4f;
}

.statusIndicator.pending.mediumPriority {
  border-color: #faad14;
}

.statusIndicator.pending.lowPriority {
  border-color: #8c8c8c;
}

.statusIndicator:hover {
  transform: scale(1.1);
}

/* 任务内容 */
.todoContent {
  flex: 1;
  margin-left: 12px;
}

.todoTitle {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.4;
}

.todoDescription {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
  line-height: 1.5;
}

.todoMeta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #bfbfbf;
}

/* 任务操作按钮 */
.todoActions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.todoItem:hover .todoActions {
  opacity: 1;
}

.actionButton {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.04);
}

.actionButton:hover {
  transform: scale(1.1);
}

.actionButton.edit {
  color: #1890ff;
}

.actionButton.edit:hover {
  background: rgba(24, 144, 255, 0.1);
}

.actionButton.delete {
  color: #ff4d4f;
}

.actionButton.delete:hover {
  background: rgba(255, 77, 79, 0.1);
}

/* 优先级标签 */
.priorityBadge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priorityBadge.high {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border: 1px solid rgba(255, 77, 79, 0.2);
}

.priorityBadge.medium {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
  border: 1px solid rgba(250, 173, 20, 0.2);
}

.priorityBadge.low {
  background: rgba(140, 140, 140, 0.1);
  color: #8c8c8c;
  border: 1px solid rgba(140, 140, 140, 0.2);
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .todoContainer {
    padding: 16px;
    border-radius: 12px;
  }
  
  .statsContainer {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .statCard {
    padding: 12px;
  }
  
  .statValue {
    font-size: 24px;
  }
  
  .todoItem {
    padding: 12px;
  }
  
  .todoTitle {
    font-size: 20px !important;
  }
}

@media (max-width: 480px) {
  .statsContainer {
    grid-template-columns: 1fr;
  }
  
  .actionContainer {
    padding: 12px;
  }
  
  .todoActions {
    opacity: 1;
  }
}
