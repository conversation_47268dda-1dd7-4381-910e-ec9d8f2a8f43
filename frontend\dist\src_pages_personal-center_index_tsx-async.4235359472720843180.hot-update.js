globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/TodoManagement.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _todo = __mako_require__("src/services/todo.ts");
            var _TodoManagementmodulecssasmodule = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TodoManagement.module.css?asmodule"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Text } = _antd.Typography;
            const { TabPane } = _antd.Tabs;
            const TodoManagement = ()=>{
                _s();
                // TODO数据状态管理
                const [personalTasks, setPersonalTasks] = (0, _react.useState)([]);
                const [todoStats, setTodoStats] = (0, _react.useState)({
                    highPriorityCount: 0,
                    mediumPriorityCount: 0,
                    lowPriorityCount: 0,
                    totalCount: 0,
                    completedCount: 0,
                    completionPercentage: 0
                });
                const [loading, setLoading] = (0, _react.useState)(true);
                const [error, setError] = (0, _react.useState)(null);
                // 待办事项状态管理
                const [todoModalVisible, setTodoModalVisible] = (0, _react.useState)(false);
                const [todoForm] = _antd.Form.useForm();
                const [editingTodoId, setEditingTodoId] = (0, _react.useState)(null);
                // 过滤器状态
                const [activeTab, setActiveTab] = (0, _react.useState)('pending');
                const [searchText, setSearchText] = (0, _react.useState)('');
                // 获取TODO数据
                (0, _react.useEffect)(()=>{
                    const fetchTodoData = async ()=>{
                        try {
                            setLoading(true);
                            setError(null);
                            console.log('TodoManagement: 开始获取TODO数据');
                            // 分别获取TODO列表和统计数据，避免一个失败影响另一个
                            const todosPromise = _todo.TodoService.getUserTodos().catch((error)=>{
                                console.error('获取TODO列表失败:', error);
                                return [];
                            });
                            const statsPromise = _todo.TodoService.getTodoStats().catch((error)=>{
                                console.error('获取TODO统计失败:', error);
                                return {
                                    highPriorityCount: 0,
                                    mediumPriorityCount: 0,
                                    lowPriorityCount: 0,
                                    totalCount: 0,
                                    completedCount: 0,
                                    completionPercentage: 0
                                };
                            });
                            const [todos, stats] = await Promise.all([
                                todosPromise,
                                statsPromise
                            ]);
                            console.log('TodoManagement: 获取到TODO列表:', todos);
                            console.log('TodoManagement: 获取到统计数据:', stats);
                            setPersonalTasks(todos);
                            setTodoStats(stats);
                        } catch (error) {
                            console.error('获取TODO数据时发生未知错误:', error);
                            setError('获取TODO数据失败，请刷新页面重试');
                        } finally{
                            setLoading(false);
                        }
                    };
                    fetchTodoData();
                }, []);
                // 根据激活的标签和搜索文本过滤任务
                const filteredPersonalTasks = (personalTasks || []).filter((task)=>{
                    // 根据标签过滤
                    if (activeTab === 'pending' && task.status === 1) return false;
                    if (activeTab === 'completed' && task.status === 0) return false;
                    // 根据搜索文本过滤
                    if (searchText && !task.title.toLowerCase().includes(searchText.toLowerCase())) return false;
                    return true;
                });
                // 处理待办事项操作
                const handleToggleTodoStatus = async (id)=>{
                    try {
                        const task = personalTasks.find((t)=>t.id === id);
                        if (!task) return;
                        const newStatus = task.status === 0 ? 1 : 0;
                        await _todo.TodoService.updateTodo(id, {
                            status: newStatus
                        });
                        // 更新本地状态
                        setPersonalTasks(personalTasks.map((task)=>task.id === id ? {
                                ...task,
                                status: newStatus
                            } : task));
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                        // 统计数据刷新失败不影响主要操作
                        }
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                const handleAddOrUpdateTodo = async (values)=>{
                    try {
                        if (editingTodoId) {
                            // 更新现有待办事项
                            const updatedTodo = await _todo.TodoService.updateTodo(editingTodoId, {
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks(personalTasks.map((task)=>task.id === editingTodoId ? updatedTodo : task));
                        } else {
                            // 添加新待办事项
                            const newTodo = await _todo.TodoService.createTodo({
                                title: values.name,
                                priority: values.priority
                            });
                            setPersonalTasks([
                                newTodo,
                                ...personalTasks
                            ]);
                        }
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                        // 统计数据刷新失败不影响主要操作
                        }
                        // 重置表单并关闭模态框
                        setTodoModalVisible(false);
                        setEditingTodoId(null);
                        todoForm.resetFields();
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                const handleDeleteTodo = async (id)=>{
                    try {
                        await _todo.TodoService.deleteTodo(id);
                        setPersonalTasks(personalTasks.filter((task)=>task.id !== id));
                        // 刷新统计数据
                        try {
                            const stats = await _todo.TodoService.getTodoStats();
                            setTodoStats(stats);
                        } catch (statsError) {
                        // 统计数据刷新失败不影响主要操作
                        }
                    } catch (error) {
                    // 错误处理由响应拦截器统一处理
                    }
                };
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: _TodoManagementmodulecssasmodule.default.todoContainer,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: _TodoManagementmodulecssasmodule.default.decorativeElement
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 219,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: _TodoManagementmodulecssasmodule.default.decorativeElement
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 220,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                position: 'relative',
                                zIndex: 1,
                                marginBottom: 24
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                    level: 3,
                                    className: _TodoManagementmodulecssasmodule.default.todoTitle,
                                    children: "待办事项管理"
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 224,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                    className: _TodoManagementmodulecssasmodule.default.todoSubtitle,
                                    children: "高效管理您的任务，提升工作效率"
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 227,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 223,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: _TodoManagementmodulecssasmodule.default.statsContainer,
                            style: {
                                position: 'relative',
                                zIndex: 1
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: _TodoManagementmodulecssasmodule.default.statCard,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: _TodoManagementmodulecssasmodule.default.statValue,
                                            children: todoStats.totalCount
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 234,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: _TodoManagementmodulecssasmodule.default.statLabel,
                                            children: "总任务"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 235,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 233,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: _TodoManagementmodulecssasmodule.default.statCard,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: _TodoManagementmodulecssasmodule.default.statValue,
                                            children: todoStats.completedCount
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 238,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: _TodoManagementmodulecssasmodule.default.statLabel,
                                            children: "已完成"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 239,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 237,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: _TodoManagementmodulecssasmodule.default.statCard,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: _TodoManagementmodulecssasmodule.default.statValue,
                                            children: todoStats.highPriorityCount
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 242,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: _TodoManagementmodulecssasmodule.default.statLabel,
                                            children: "高优先级"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 243,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 241,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: _TodoManagementmodulecssasmodule.default.statCard,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: _TodoManagementmodulecssasmodule.default.statValue,
                                            children: todoStats.mediumPriorityCount
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 246,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: _TodoManagementmodulecssasmodule.default.statLabel,
                                            children: "中优先级"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 247,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 245,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: _TodoManagementmodulecssasmodule.default.statCard,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: _TodoManagementmodulecssasmodule.default.statValue,
                                            children: todoStats.lowPriorityCount
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 250,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: _TodoManagementmodulecssasmodule.default.statLabel,
                                            children: "低优先级"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 251,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 249,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 232,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: _TodoManagementmodulecssasmodule.default.progressContainer,
                            style: {
                                position: 'relative',
                                zIndex: 1
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: _TodoManagementmodulecssasmodule.default.progressLabel,
                                    children: [
                                        "任务完成进度 ",
                                        todoStats.completedCount,
                                        "/",
                                        todoStats.totalCount
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 257,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                    percent: todoStats.completionPercentage,
                                    strokeColor: {
                                        '0%': '#4facfe',
                                        '100%': '#00f2fe'
                                    },
                                    trailColor: "rgba(255, 255, 255, 0.2)",
                                    showInfo: false,
                                    strokeWidth: 8
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 260,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        textAlign: 'right',
                                        marginTop: 8
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                        style: {
                                            color: 'white',
                                            fontSize: 16,
                                            fontWeight: 600
                                        },
                                        children: [
                                            todoStats.completionPercentage,
                                            "%"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 271,
                                        columnNumber: 11
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 270,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 256,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: _TodoManagementmodulecssasmodule.default.actionContainer,
                            style: {
                                position: 'relative',
                                zIndex: 1
                            },
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                gutter: [
                                    16,
                                    16
                                ],
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        xs: 24,
                                        sm: 16,
                                        md: 18,
                                        lg: 20,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Search, {
                                            placeholder: "搜索任务...",
                                            allowClear: true,
                                            prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 284,
                                                columnNumber: 23
                                            }, void 0),
                                            value: searchText,
                                            onChange: (e)=>setSearchText(e.target.value),
                                            className: _TodoManagementmodulecssasmodule.default.searchInput,
                                            size: "large"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 281,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 280,
                                        columnNumber: 11
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                        xs: 24,
                                        sm: 8,
                                        md: 6,
                                        lg: 4,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "primary",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 294,
                                                columnNumber: 21
                                            }, void 0),
                                            className: _TodoManagementmodulecssasmodule.default.addButton,
                                            size: "large",
                                            block: true,
                                            onClick: ()=>{
                                                setEditingTodoId(null);
                                                todoForm.resetFields();
                                                setTodoModalVisible(true);
                                            },
                                            children: "添加新任务"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 292,
                                            columnNumber: 13
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 291,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 279,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 278,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                            activeKey: activeTab,
                            onChange: (key)=>setActiveTab(key),
                            size: "large",
                            className: _TodoManagementmodulecssasmodule.default.todoTabs,
                            style: {
                                position: 'relative',
                                zIndex: 1,
                                marginBottom: 16
                            },
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "全部任务"
                                }, "all", false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 318,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "待处理"
                                }, "pending", false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 319,
                                    columnNumber: 9
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                                    tab: "已完成"
                                }, "completed", false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 320,
                                    columnNumber: 9
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 311,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: _TodoManagementmodulecssasmodule.default.todoListContainer,
                            style: {
                                position: 'relative',
                                zIndex: 1
                            },
                            children: error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                message: "TODO数据加载失败",
                                description: error,
                                type: "error",
                                showIcon: true,
                                style: {
                                    marginBottom: 16
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 326,
                                columnNumber: 11
                            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                spinning: loading,
                                children: filteredPersonalTasks.length === 0 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        textAlign: 'center',
                                        padding: '40px 0',
                                        color: 'rgba(255, 255, 255, 0.6)'
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                        style: {
                                            color: 'rgba(255, 255, 255, 0.6)'
                                        },
                                        children: "暂无任务数据"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 337,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 336,
                                    columnNumber: 15
                                }, this) : filteredPersonalTasks.map((item)=>{
                                    const priorityClass = item.priority === 3 ? 'highPriority' : item.priority === 2 ? 'mediumPriority' : 'lowPriority';
                                    const statusClass = item.status === 1 ? 'completed' : 'pending';
                                    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        className: `${_TodoManagementmodulecssasmodule.default.todoItem} ${_TodoManagementmodulecssasmodule.default[priorityClass]} ${item.status === 1 ? _TodoManagementmodulecssasmodule.default.completed : ''}`,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            gap: 16,
                                            style: {
                                                width: '100%'
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    className: `${_TodoManagementmodulecssasmodule.default.statusIndicator} ${_TodoManagementmodulecssasmodule.default[statusClass]} ${_TodoManagementmodulecssasmodule.default[priorityClass]}`,
                                                    onClick: ()=>handleToggleTodoStatus(item.id, item.status === 1 ? 0 : 1),
                                                    children: item.status === 1 && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                        style: {
                                                            color: '#fff',
                                                            fontSize: 12
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 359,
                                                        columnNumber: 27
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 354,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    className: _TodoManagementmodulecssasmodule.default.todoContent,
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            className: _TodoManagementmodulecssasmodule.default.todoTitle,
                                                            style: {
                                                                textDecoration: item.status === 1 ? 'line-through' : 'none',
                                                                color: item.status === 1 ? '#8c8c8c' : '#262626'
                                                            },
                                                            children: item.title
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 365,
                                                            columnNumber: 25
                                                        }, this),
                                                        item.description && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            className: _TodoManagementmodulecssasmodule.default.todoDescription,
                                                            children: item.description
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 372,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            className: _TodoManagementmodulecssasmodule.default.todoMeta,
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {}, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 378,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                    children: [
                                                                        "创建于 ",
                                                                        new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 379,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                    className: `${_TodoManagementmodulecssasmodule.default.priorityBadge} ${_TodoManagementmodulecssasmodule.default[priorityClass.replace('Priority', '')]}`,
                                                                    children: item.priority === 3 ? '高优先级' : item.priority === 2 ? '中优先级' : '低优先级'
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                    lineNumber: 380,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 377,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 364,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    className: _TodoManagementmodulecssasmodule.default.todoActions,
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("button", {
                                                            className: `${_TodoManagementmodulecssasmodule.default.actionButton} ${_TodoManagementmodulecssasmodule.default.edit}`,
                                                            onClick: (e)=>{
                                                                e.stopPropagation();
                                                                setEditingTodoId(item.id);
                                                                todoForm.setFieldsValue({
                                                                    name: item.title,
                                                                    priority: item.priority
                                                                });
                                                                setTodoModalVisible(true);
                                                            },
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 400,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 388,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("button", {
                                                            className: `${_TodoManagementmodulecssasmodule.default.actionButton} ${_TodoManagementmodulecssasmodule.default.delete}`,
                                                            onClick: (e)=>{
                                                                e.stopPropagation();
                                                                handleDeleteTodo(item.id);
                                                            },
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                                lineNumber: 409,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 402,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 387,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 352,
                                            columnNumber: 21
                                        }, this)
                                    }, item.id, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 348,
                                        columnNumber: 19
                                    }, this);
                                })
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 334,
                                columnNumber: 11
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 324,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ModalForm, {
                            title: editingTodoId ? '编辑待办事项' : '新增待办事项',
                            open: todoModalVisible,
                            onOpenChange: (visible)=>{
                                setTodoModalVisible(visible);
                                if (!visible) {
                                    setEditingTodoId(null);
                                    todoForm.resetFields();
                                }
                            },
                            form: todoForm,
                            layout: "vertical",
                            onFinish: handleAddOrUpdateTodo,
                            autoComplete: "off",
                            width: 500,
                            modalProps: {
                                centered: true,
                                destroyOnClose: true,
                                maskClosable: true,
                                keyboard: true,
                                forceRender: false
                            },
                            submitter: {
                                searchConfig: {
                                    submitText: editingTodoId ? '更新任务' : '创建任务',
                                    resetText: '取消'
                                },
                                submitButtonProps: {
                                    style: {
                                        background: '#1890ff',
                                        borderColor: '#1890ff',
                                        boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)'
                                    },
                                    icon: editingTodoId ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 454,
                                        columnNumber: 39
                                    }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 454,
                                        columnNumber: 58
                                    }, void 0)
                                },
                                resetButtonProps: {
                                    style: {
                                        borderColor: '#d9d9d9'
                                    }
                                },
                                onReset: ()=>{
                                    setTodoModalVisible(false);
                                    setEditingTodoId(null);
                                    todoForm.resetFields();
                                }
                            },
                            preserve: false,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "name",
                                    label: "任务名称",
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入任务名称'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入任务名称",
                                        size: "large",
                                        style: {
                                            borderRadius: 6
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 474,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 469,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    name: "priority",
                                    label: "优先级",
                                    initialValue: 2,
                                    rules: [
                                        {
                                            required: true,
                                            message: '请选择优先级'
                                        }
                                    ],
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                        size: "large",
                                        options: [
                                            {
                                                value: 3,
                                                label: '高优先级'
                                            },
                                            {
                                                value: 2,
                                                label: '中优先级'
                                            },
                                            {
                                                value: 1,
                                                label: '低优先级'
                                            }
                                        ],
                                        style: {
                                            borderRadius: 6
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 487,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 481,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                            lineNumber: 421,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                    lineNumber: 217,
                    columnNumber: 5
                }, this);
            };
            _s(TodoManagement, "BRDE78r/O6qdRZ096tTY2qeEVcA=", false, function() {
                return [
                    _antd.Form.useForm
                ];
            });
            _c = TodoManagement;
            var _default = TodoManagement;
            var _c;
            $RefreshReg$(_c, "TodoManagement");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '15873477621211578221';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.4235359472720843180.hot-update.js.map