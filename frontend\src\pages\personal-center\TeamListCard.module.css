/* 团队列表卡片样式 */
.teamListCard {
  margin-bottom: 24px;
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}

.teamListCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

/* 装饰性背景元素 */
.decorativeCircle1 {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 1;
  animation: float 6s ease-in-out infinite;
}

.decorativeCircle2 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  z-index: 1;
  animation: float 8s ease-in-out infinite reverse;
}

/* 主要内容区域 */
.contentArea {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  margin: 2px;
  border-radius: 14px;
  padding: 24px;
  transition: all 0.3s ease;
}

/* 标题栏 */
.titleBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  margin: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
}

/* 团队卡片 */
.teamItem {
  margin-bottom: 16px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.teamItem:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.teamItem:active {
  transform: translateY(-2px) scale(0.98);
  transition: all 0.1s ease;
}

/* 当前团队样式 */
.currentTeam {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f4ff 100%);
  border: 2px solid #91caff;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.2);
}

.currentTeam::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #722ed1);
  z-index: 1;
}

/* 普通团队样式 */
.normalTeam {
  background: #ffffff;
  border: 1px solid #f0f0f0;
}

/* 团队创建者标识 */
.creatorTeam {
  border-left: 4px solid #722ed1;
}

.memberTeam {
  border-left: 4px solid #52c41a;
}

/* 团队信息区域 */
.teamInfo {
  padding: 20px 24px;
}

.teamName {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.teamName:hover {
  color: #1890ff;
  transform: translateX(4px);
}

.teamMeta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.teamMetaItem {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #8c8c8c;
}

/* 团队状态标签 */
.teamBadges {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.currentBadge {
  background: linear-gradient(135deg, #1890ff, #722ed1);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  animation: pulse 2s infinite;
}

.creatorBadge {
  background: linear-gradient(135deg, #722ed1, #eb2f96);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.memberBadge {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 团队统计数据 */
.teamStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.statItem {
  text-align: center;
  padding: 12px 8px;
  background: rgba(24, 144, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
}

.statItem:hover {
  background: rgba(24, 144, 255, 0.1);
  transform: translateY(-2px);
}

.statValue {
  font-size: 20px;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
  margin-bottom: 4px;
}

.statLabel {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
}

/* 团队操作按钮 */
.teamActions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.actionButton {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.actionButton:hover {
  transform: scale(1.05);
}

/* 加载状态 */
.loadingContainer {
  animation: fadeInUp 0.6s ease-out;
}

.switchingTeam {
  opacity: 0.6;
  pointer-events: none;
}

/* 空状态 */
.emptyState {
  text-align: center;
  padding: 60px 20px;
  color: #8c8c8c;
}

.emptyIcon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 渐入动画延迟 */
.fadeInDelay1 {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.fadeInDelay2 {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.fadeInDelay3 {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contentArea {
    padding: 16px;
  }
  
  .teamInfo {
    padding: 16px 20px;
  }
  
  .teamName {
    font-size: 16px;
  }
  
  .teamStats {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .teamMeta {
    gap: 12px;
  }
}

@media (max-width: 576px) {
  .teamListCard {
    margin-bottom: 16px;
    border-radius: 12px;
  }
  
  .contentArea {
    padding: 12px;
    border-radius: 10px;
  }
  
  .teamInfo {
    padding: 12px 16px;
  }
  
  .teamStats {
    grid-template-columns: 1fr 1fr;
  }
  
  .teamBadges {
    gap: 6px;
  }
}
