import {
  ClockCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  SettingOutlined,
  TeamOutlined,
  UserOutlined,
  CarOutlined,
  UsergroupAddOutlined,
  ExclamationCircleOutlined,
  AlertOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Avatar,
  Button,
  Card,
  Col,
  Row,
  Space,
  Spin,
  Typography,
} from 'antd';
import { ProCard } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserProfileDetailResponse, UserPersonalStatsResponse } from '@/types/api';
import UnifiedSettingsModal from './UnifiedSettingsModal';

const { Title, Text } = Typography;

/**
 * 个人信息组件
 *
 * 显示用户的完整个人信息，包括基本信息和登录历史。
 * 整合了原UserProfileCard和LastLoginInfo组件的功能。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名、邮箱、电话
 * 3. 显示注册日期
 * 4. 显示最后登录时间和登录团队
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */
const PersonalInfo: React.FC = () => {
  /**
   * 用户详细信息状态管理
   */
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: '',
    position: '',
    email: '',
    telephone: '',
    registerDate: '',
    lastLoginTime: '',
    lastLoginTeam: '',
    teamCount: 0,
    avatar: '',
  });

  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // 数据概览状态管理
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // Modal状态管理
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);

  // 获取用户数据和统计数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userDetail = await UserService.getUserProfileDetail();
        setUserInfo(userDetail);
        setUserInfoError(null);
      } catch (error) {
        console.error('获取用户详细信息失败:', error);
        setUserInfoError('获取用户详细信息失败，请稍后重试');
      } finally {
        setUserInfoLoading(false);
      }
    };

    const fetchStatsData = async () => {
      try {
        const stats = await UserService.getUserPersonalStats();
        setPersonalStats(stats);
        setStatsError(null);
      } catch (error) {
        console.error('获取统计数据失败:', error);
        setStatsError('获取统计数据失败，请稍后重试');
      } finally {
        setStatsLoading(false);
      }
    };

    fetchUserData();
    fetchStatsData();
  }, []);

  return (
    <Card
      style={{
        marginBottom: 24,
        borderRadius: 16,
        border: 'none',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.08)',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        overflow: 'hidden',
        position: 'relative',
      }}
      styles={{
        body: {
          padding: 0,
        },
      }}
    >
      {/* 装饰性背景元素 */}
      <div
        style={{
          position: 'absolute',
          top: -50,
          right: -50,
          width: 120,
          height: 120,
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '50%',
          zIndex: 1,
        }}
      />
      <div
        style={{
          position: 'absolute',
          bottom: -30,
          left: -30,
          width: 80,
          height: 80,
          background: 'rgba(255, 255, 255, 0.05)',
          borderRadius: '50%',
          zIndex: 1,
        }}
      />

      {/* 主要内容区域 */}
      <div
        style={{
          position: 'relative',
          zIndex: 2,
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          margin: '2px',
          borderRadius: '14px',
          padding: '24px',
        }}
      >
        {/* 标题栏 */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 24,
            paddingBottom: 16,
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <Typography.Title
            level={4}
            style={{
              margin: 0,
              background: 'linear-gradient(135deg, #667eea, #764ba2)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 600,
            }}
          >
            个人信息
          </Typography.Title>
          <Button
            type="text"
            icon={<SettingOutlined />}
            onClick={() => setSettingsModalVisible(true)}
            style={{
              borderRadius: 8,
              width: 32,
              height: 32,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#667eea',
              transition: 'all 0.3s ease',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(102, 126, 234, 0.1)';
              e.currentTarget.style.transform = 'scale(1.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'transparent';
              e.currentTarget.style.transform = 'scale(1)';
            }}
          />
        </div>
        {userInfoError ? (
          <Alert
            message="个人信息加载失败"
            description={userInfoError}
            type="error"
            showIcon
            style={{
              borderRadius: 12,
              border: 'none',
            }}
          />
        ) : (
          <Spin spinning={userInfoLoading || statsLoading}>
            {/* 主要内容区域 */}
            <Row gutter={[32, 24]} align="top">
              {/* 左侧：个人信息 */}
              <Col xs={24} sm={24} md={14} lg={14} xl={14}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: 20,
                    marginBottom: 24,
                  }}
                >
                  {/* 头像区域 */}
                  <div style={{ position: 'relative' }}>
                    <Avatar
                      size={80}
                      style={{
                        background: 'linear-gradient(135deg, #667eea, #764ba2)',
                        fontSize: 28,
                        fontWeight: 600,
                        border: '4px solid rgba(102, 126, 234, 0.1)',
                        boxShadow: '0 8px 24px rgba(102, 126, 234, 0.2)',
                      }}
                      icon={!userInfo.name && <UserOutlined />}
                    >
                      {userInfo.name && userInfo.name.charAt(0).toUpperCase()}
                    </Avatar>
                    {/* 在线状态指示器 */}
                    <div
                      style={{
                        position: 'absolute',
                        bottom: 4,
                        right: 4,
                        width: 16,
                        height: 16,
                        background: '#52c41a',
                        borderRadius: '50%',
                        border: '2px solid white',
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
                      }}
                    />
                  </div>

                  {/* 基本信息 */}
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <Typography.Title
                      level={3}
                      style={{
                        margin: '0 0 8px 0',
                        fontSize: 24,
                        fontWeight: 600,
                        color: '#262626',
                        lineHeight: 1.2,
                      }}
                    >
                      {userInfo.name || '加载中...'}
                    </Typography.Title>

                    {/* 联系信息 */}
                    <Space direction="vertical" size={12}>
                      {userInfo.email && (
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8,
                            padding: '8px 12px',
                            background: 'rgba(24, 144, 255, 0.05)',
                            borderRadius: 8,
                            border: '1px solid rgba(24, 144, 255, 0.1)',
                          }}
                        >
                          <MailOutlined
                            style={{
                              fontSize: 16,
                              color: '#1890ff',
                            }}
                          />
                          <Typography.Text
                            style={{
                              color: '#595959',
                              fontSize: 14,
                              fontWeight: 500,
                            }}
                          >
                            {userInfo.email}
                          </Typography.Text>
                        </div>
                      )}
                      {userInfo.telephone && (
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8,
                            padding: '8px 12px',
                            background: 'rgba(82, 196, 26, 0.05)',
                            borderRadius: 8,
                            border: '1px solid rgba(82, 196, 26, 0.1)',
                          }}
                        >
                          <PhoneOutlined
                            style={{
                              fontSize: 16,
                              color: '#52c41a',
                            }}
                          />
                          <Typography.Text
                            style={{
                              color: '#595959',
                              fontSize: 14,
                              fontWeight: 500,
                            }}
                          >
                            {userInfo.telephone}
                          </Typography.Text>
                        </div>
                      )}
                    </Space>
                  </div>
                </div>

                {/* 附加信息区域 */}
                <div
                  style={{
                    marginTop: 20,
                    padding: '16px',
                    background: '#fafafa',
                    borderRadius: 12,
                    border: '1px solid #f0f0f0',
                  }}
                >
                  <Space direction="vertical" size={8} style={{ width: '100%' }}>
                    {userInfo.registerDate && (
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <Typography.Text
                          style={{
                            fontSize: 13,
                            color: '#8c8c8c',
                            fontWeight: 500,
                          }}
                        >
                          📅 注册于 {userInfo.registerDate}
                        </Typography.Text>
                      </div>
                    )}
                    {userInfo.lastLoginTime && (
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <ClockCircleOutlined
                          style={{
                            fontSize: 13,
                            color: '#1890ff',
                          }}
                        />
                        <Typography.Text
                          style={{
                            fontSize: 13,
                            color: '#8c8c8c',
                            fontWeight: 500,
                          }}
                        >
                          最后登录：{userInfo.lastLoginTime}
                        </Typography.Text>
                      </div>
                    )}
                    {userInfo.lastLoginTeam && (
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <TeamOutlined
                          style={{
                            fontSize: 13,
                            color: '#52c41a',
                          }}
                        />
                        <Typography.Text
                          style={{
                            fontSize: 13,
                            color: '#8c8c8c',
                            fontWeight: 500,
                          }}
                        >
                          团队：{userInfo.lastLoginTeam}
                        </Typography.Text>
                      </div>
                    )}
                  </Space>
                </div>
              </Col>

              {/* 右侧：数据概览 */}
              <Col xs={24} sm={24} md={10} lg={10} xl={10}>
                {statsError ? (
                  <Alert
                    message="数据概览加载失败"
                    description={statsError}
                    type="error"
                    showIcon
                    style={{
                      borderRadius: 12,
                      border: 'none',
                    }}
                  />
                ) : (
                  <div>
                    <Typography.Title
                      level={5}
                      style={{
                        margin: '0 0 16px 0',
                        color: '#595959',
                        fontWeight: 600,
                      }}
                    >
                      数据概览
                    </Typography.Title>
                    <Row gutter={[12, 12]}>
                      {/* 车辆统计 */}
                      <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                        <Card
                          size="small"
                          style={{
                            borderRadius: 12,
                            border: 'none',
                            background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
                            boxShadow: '0 4px 12px rgba(24, 144, 255, 0.15)',
                            transition: 'all 0.3s ease',
                            cursor: 'pointer',
                          }}
                          styles={{
                            body: {
                              padding: '16px 12px',
                              textAlign: 'center',
                            },
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 8px 24px rgba(24, 144, 255, 0.25)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(24, 144, 255, 0.15)';
                          }}
                        >
                          <div style={{ marginBottom: 8 }}>
                            <CarOutlined
                              style={{
                                fontSize: 20,
                                color: '#1890ff',
                                marginBottom: 4,
                              }}
                            />
                          </div>
                          <div
                            style={{
                              fontSize: 24,
                              fontWeight: 700,
                              color: '#1890ff',
                              lineHeight: 1,
                              marginBottom: 4,
                            }}
                          >
                            {personalStats.vehicles}
                          </div>
                          <div
                            style={{
                              fontSize: 12,
                              color: '#1890ff',
                              fontWeight: 600,
                              opacity: 0.8,
                            }}
                          >
                            车辆
                          </div>
                        </Card>
                      </Col>

                      {/* 人员统计 */}
                      <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                        <Card
                          size="small"
                          style={{
                            borderRadius: 12,
                            border: 'none',
                            background: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
                            boxShadow: '0 4px 12px rgba(82, 196, 26, 0.15)',
                            transition: 'all 0.3s ease',
                            cursor: 'pointer',
                          }}
                          styles={{
                            body: {
                              padding: '16px 12px',
                              textAlign: 'center',
                            },
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 8px 24px rgba(82, 196, 26, 0.25)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(82, 196, 26, 0.15)';
                          }}
                        >
                          <div style={{ marginBottom: 8 }}>
                            <UsergroupAddOutlined
                              style={{
                                fontSize: 20,
                                color: '#52c41a',
                                marginBottom: 4,
                              }}
                            />
                          </div>
                          <div
                            style={{
                              fontSize: 24,
                              fontWeight: 700,
                              color: '#52c41a',
                              lineHeight: 1,
                              marginBottom: 4,
                            }}
                          >
                            {personalStats.personnel}
                          </div>
                          <div
                            style={{
                              fontSize: 12,
                              color: '#52c41a',
                              fontWeight: 600,
                              opacity: 0.8,
                            }}
                          >
                            人员
                          </div>
                        </Card>
                      </Col>

                      {/* 预警统计 */}
                      <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                        <Card
                          size="small"
                          style={{
                            borderRadius: 12,
                            border: 'none',
                            background: 'linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%)',
                            boxShadow: '0 4px 12px rgba(250, 173, 20, 0.15)',
                            transition: 'all 0.3s ease',
                            cursor: 'pointer',
                          }}
                          styles={{
                            body: {
                              padding: '16px 12px',
                              textAlign: 'center',
                            },
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 8px 24px rgba(250, 173, 20, 0.25)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(250, 173, 20, 0.15)';
                          }}
                        >
                          <div style={{ marginBottom: 8 }}>
                            <ExclamationCircleOutlined
                              style={{
                                fontSize: 20,
                                color: '#faad14',
                                marginBottom: 4,
                              }}
                            />
                          </div>
                          <div
                            style={{
                              fontSize: 24,
                              fontWeight: 700,
                              color: '#faad14',
                              lineHeight: 1,
                              marginBottom: 4,
                            }}
                          >
                            {personalStats.warnings}
                          </div>
                          <div
                            style={{
                              fontSize: 12,
                              color: '#faad14',
                              fontWeight: 600,
                              opacity: 0.8,
                            }}
                          >
                            预警
                          </div>
                        </Card>
                      </Col>

                      {/* 告警统计 */}
                      <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                        <Card
                          size="small"
                          style={{
                            borderRadius: 12,
                            border: 'none',
                            background: 'linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%)',
                            boxShadow: '0 4px 12px rgba(255, 77, 79, 0.15)',
                            transition: 'all 0.3s ease',
                            cursor: 'pointer',
                          }}
                          styles={{
                            body: {
                              padding: '16px 12px',
                              textAlign: 'center',
                            },
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = '0 8px 24px rgba(255, 77, 79, 0.25)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.15)';
                          }}
                        >
                          <div style={{ marginBottom: 8 }}>
                            <AlertOutlined
                              style={{
                                fontSize: 20,
                                color: '#ff4d4f',
                                marginBottom: 4,
                              }}
                            />
                          </div>
                          <div
                            style={{
                              fontSize: 24,
                              fontWeight: 700,
                              color: '#ff4d4f',
                              lineHeight: 1,
                              marginBottom: 4,
                            }}
                          >
                            {personalStats.alerts}
                          </div>
                          <div
                            style={{
                              fontSize: 12,
                              color: '#ff4d4f',
                              fontWeight: 600,
                              opacity: 0.8,
                            }}
                          >
                            告警
                          </div>
                        </Card>
                      </Col>
                    </Row>
                  </div>
                )}
              </Col>
          </Row>
        </Spin>
      )}

      {/* 统一设置Modal */}
      <UnifiedSettingsModal
        visible={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        userInfo={userInfo}
        onSuccess={() => {
          // 可以在这里刷新用户信息或团队列表
          console.log('设置操作成功');
        }}
      />
    </ProCard>
  );
};

export default PersonalInfo;
