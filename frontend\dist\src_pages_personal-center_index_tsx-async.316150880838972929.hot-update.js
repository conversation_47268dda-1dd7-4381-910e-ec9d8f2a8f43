globalThis.makoModuleHotUpdate('src/pages/personal-center/index.tsx', {
    modules: {
        "src/pages/personal-center/PersonalInfo.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _user = __mako_require__("src/services/user.ts");
            var _UnifiedSettingsModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UnifiedSettingsModal.tsx"));
            var _PersonalInfomodulecssasmodule = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/PersonalInfo.module.css?asmodule"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _s = $RefreshSig$();
            const { Title, Text } = _antd.Typography;
            /**
 * 个人信息组件
 *
 * 显示用户的完整个人信息，包括基本信息和登录历史。
 * 整合了原UserProfileCard和LastLoginInfo组件的功能。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名、邮箱、电话
 * 3. 显示注册日期
 * 4. 显示最后登录时间和登录团队
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */ const PersonalInfo = ()=>{
                var _userInfo_name;
                _s();
                /**
   * 用户详细信息状态管理
   */ const [userInfo, setUserInfo] = (0, _react.useState)({
                    name: '',
                    position: '',
                    email: '',
                    telephone: '',
                    registerDate: '',
                    lastLoginTime: '',
                    lastLoginTeam: '',
                    teamCount: 0,
                    avatar: ''
                });
                const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
                const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
                // 数据概览状态管理
                const [personalStats, setPersonalStats] = (0, _react.useState)({
                    vehicles: 0,
                    personnel: 0,
                    warnings: 0,
                    alerts: 0
                });
                const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
                const [statsError, setStatsError] = (0, _react.useState)(null);
                // Modal状态管理
                const [settingsModalVisible, setSettingsModalVisible] = (0, _react.useState)(false);
                // 获取用户数据和统计数据
                (0, _react.useEffect)(()=>{
                    const fetchUserData = async ()=>{
                        try {
                            const userDetail = await _user.UserService.getUserProfileDetail();
                            setUserInfo(userDetail);
                            setUserInfoError(null);
                        } catch (error) {
                            console.error('获取用户详细信息失败:', error);
                            setUserInfoError('获取用户详细信息失败，请稍后重试');
                        } finally{
                            setUserInfoLoading(false);
                        }
                    };
                    const fetchStatsData = async ()=>{
                        try {
                            const stats = await _user.UserService.getUserPersonalStats();
                            setPersonalStats(stats);
                            setStatsError(null);
                        } catch (error) {
                            console.error('获取统计数据失败:', error);
                            setStatsError('获取统计数据失败，请稍后重试');
                        } finally{
                            setStatsLoading(false);
                        }
                    };
                    fetchUserData();
                    fetchStatsData();
                }, []);
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                    className: _PersonalInfomodulecssasmodule.default.personalInfoCard,
                    styles: {
                        body: {
                            padding: 0
                        }
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: _PersonalInfomodulecssasmodule.default.decorativeCircle1
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 122,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: _PersonalInfomodulecssasmodule.default.decorativeCircle2
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 123,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            className: `${_PersonalInfomodulecssasmodule.default.contentArea} ${_PersonalInfomodulecssasmodule.default.loadingContainer}`,
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    className: _PersonalInfomodulecssasmodule.default.titleBar,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                            level: 4,
                                            className: _PersonalInfomodulecssasmodule.default.title,
                                            children: "个人信息"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 129,
                                            columnNumber: 11
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                            type: "text",
                                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 134,
                                                columnNumber: 19
                                            }, void 0),
                                            onClick: ()=>setSettingsModalVisible(true),
                                            className: _PersonalInfomodulecssasmodule.default.settingsButton
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 132,
                                            columnNumber: 11
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                    lineNumber: 128,
                                    columnNumber: 9
                                }, this),
                                userInfoError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                    message: "个人信息加载失败",
                                    description: userInfoError,
                                    type: "error",
                                    showIcon: true,
                                    style: {
                                        borderRadius: 12,
                                        border: 'none'
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                    lineNumber: 140,
                                    columnNumber: 11
                                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                    spinning: userInfoLoading || statsLoading,
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                        gutter: [
                                            32,
                                            24
                                        ],
                                        align: "top",
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                xs: 24,
                                                sm: 24,
                                                md: 14,
                                                lg: 14,
                                                xl: 14,
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            display: 'flex',
                                                            alignItems: 'flex-start',
                                                            gap: 20,
                                                            marginBottom: 24
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                className: _PersonalInfomodulecssasmodule.default.avatarContainer,
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                                        size: 80,
                                                                        className: _PersonalInfomodulecssasmodule.default.avatar,
                                                                        icon: !userInfo.name && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 169,
                                                                            columnNumber: 47
                                                                        }, void 0),
                                                                        children: (_userInfo_name = userInfo.name) === null || _userInfo_name === void 0 ? void 0 : _userInfo_name.charAt(0).toUpperCase()
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 166,
                                                                        columnNumber: 21
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                        className: _PersonalInfomodulecssasmodule.default.onlineIndicator
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 174,
                                                                        columnNumber: 21
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 165,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    flex: 1,
                                                                    minWidth: 0
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                                                        level: 3,
                                                                        style: {
                                                                            margin: '0 0 8px 0',
                                                                            fontSize: 24,
                                                                            fontWeight: 600,
                                                                            color: '#262626',
                                                                            lineHeight: 1.2
                                                                        },
                                                                        children: userInfo.name || '加载中...'
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 179,
                                                                        columnNumber: 21
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                                        direction: "vertical",
                                                                        size: 12,
                                                                        children: [
                                                                            userInfo.email && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                className: `${_PersonalInfomodulecssasmodule.default.contactCard} ${_PersonalInfomodulecssasmodule.default.emailCard}`,
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                                                        style: {
                                                                                            fontSize: 16,
                                                                                            color: '#1890ff'
                                                                                        }
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                        lineNumber: 196,
                                                                                        columnNumber: 27
                                                                                    }, this),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                                        style: {
                                                                                            color: '#595959',
                                                                                            fontSize: 14,
                                                                                            fontWeight: 500
                                                                                        },
                                                                                        children: userInfo.email
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                        lineNumber: 202,
                                                                                        columnNumber: 27
                                                                                    }, this)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 195,
                                                                                columnNumber: 25
                                                                            }, this),
                                                                            userInfo.telephone && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                className: `${_PersonalInfomodulecssasmodule.default.contactCard} ${_PersonalInfomodulecssasmodule.default.phoneCard}`,
                                                                                children: [
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                                                                        style: {
                                                                                            fontSize: 16,
                                                                                            color: '#52c41a'
                                                                                        }
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                        lineNumber: 215,
                                                                                        columnNumber: 27
                                                                                    }, this),
                                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                                        style: {
                                                                                            color: '#595959',
                                                                                            fontSize: 14,
                                                                                            fontWeight: 500
                                                                                        },
                                                                                        children: userInfo.telephone
                                                                                    }, void 0, false, {
                                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                        lineNumber: 221,
                                                                                        columnNumber: 27
                                                                                    }, this)
                                                                                ]
                                                                            }, void 0, true, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 214,
                                                                                columnNumber: 25
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 193,
                                                                        columnNumber: 21
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 178,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 156,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        className: _PersonalInfomodulecssasmodule.default.additionalInfo,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            direction: "vertical",
                                                            size: 8,
                                                            style: {
                                                                width: '100%'
                                                            },
                                                            children: [
                                                                userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        gap: 8
                                                                    },
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                        style: {
                                                                            fontSize: 13,
                                                                            color: '#8c8c8c',
                                                                            fontWeight: 500
                                                                        },
                                                                        children: [
                                                                            "📅 注册于 ",
                                                                            userInfo.registerDate
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 241,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 240,
                                                                    columnNumber: 23
                                                                }, this),
                                                                userInfo.lastLoginTime && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        gap: 8
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                            style: {
                                                                                fontSize: 13,
                                                                                color: '#1890ff'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 254,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                            style: {
                                                                                fontSize: 13,
                                                                                color: '#8c8c8c',
                                                                                fontWeight: 500
                                                                            },
                                                                            children: [
                                                                                "最后登录：",
                                                                                userInfo.lastLoginTime
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 260,
                                                                            columnNumber: 25
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 253,
                                                                    columnNumber: 23
                                                                }, this),
                                                                userInfo.lastLoginTeam && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        gap: 8
                                                                    },
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                            style: {
                                                                                fontSize: 13,
                                                                                color: '#52c41a'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 273,
                                                                            columnNumber: 25
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                            style: {
                                                                                fontSize: 13,
                                                                                color: '#8c8c8c',
                                                                                fontWeight: 500
                                                                            },
                                                                            children: [
                                                                                "团队：",
                                                                                userInfo.lastLoginTeam
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 279,
                                                                            columnNumber: 25
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 272,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 238,
                                                            columnNumber: 19
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 237,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 155,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                xs: 24,
                                                sm: 24,
                                                md: 10,
                                                lg: 10,
                                                xl: 10,
                                                children: statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                                    message: "数据概览加载失败",
                                                    description: statsError,
                                                    type: "error",
                                                    showIcon: true,
                                                    style: {
                                                        borderRadius: 12,
                                                        border: 'none'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 297,
                                                    columnNumber: 19
                                                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                                            level: 5,
                                                            className: _PersonalInfomodulecssasmodule.default.statsTitle,
                                                            children: "数据概览"
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 309,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                            gutter: [
                                                                12,
                                                                12
                                                            ],
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                    xs: 12,
                                                                    sm: 12,
                                                                    md: 12,
                                                                    lg: 12,
                                                                    xl: 12,
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                        size: "small",
                                                                        className: `${_PersonalInfomodulecssasmodule.default.statsCard} ${_PersonalInfomodulecssasmodule.default.vehicleCard}`,
                                                                        styles: {
                                                                            body: {
                                                                                padding: '16px 12px',
                                                                                textAlign: 'center'
                                                                            }
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    marginBottom: 8
                                                                                },
                                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                                                    style: {
                                                                                        fontSize: 20,
                                                                                        color: '#1890ff',
                                                                                        marginBottom: 4
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                    lineNumber: 326,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 325,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 24,
                                                                                    fontWeight: 700,
                                                                                    color: '#1890ff',
                                                                                    lineHeight: 1,
                                                                                    marginBottom: 4
                                                                                },
                                                                                children: personalStats.vehicles
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 334,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 12,
                                                                                    color: '#1890ff',
                                                                                    fontWeight: 600,
                                                                                    opacity: 0.8
                                                                                },
                                                                                children: "车辆"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 345,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 315,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 314,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                    xs: 12,
                                                                    sm: 12,
                                                                    md: 12,
                                                                    lg: 12,
                                                                    xl: 12,
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                        size: "small",
                                                                        style: {
                                                                            borderRadius: 12,
                                                                            border: 'none',
                                                                            background: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
                                                                            boxShadow: '0 4px 12px rgba(82, 196, 26, 0.15)',
                                                                            transition: 'all 0.3s ease',
                                                                            cursor: 'pointer'
                                                                        },
                                                                        styles: {
                                                                            body: {
                                                                                padding: '16px 12px',
                                                                                textAlign: 'center'
                                                                            }
                                                                        },
                                                                        onMouseEnter: (e)=>{
                                                                            e.currentTarget.style.transform = 'translateY(-2px)';
                                                                            e.currentTarget.style.boxShadow = '0 8px 24px rgba(82, 196, 26, 0.25)';
                                                                        },
                                                                        onMouseLeave: (e)=>{
                                                                            e.currentTarget.style.transform = 'translateY(0)';
                                                                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(82, 196, 26, 0.15)';
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    marginBottom: 8
                                                                                },
                                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UsergroupAddOutlined, {
                                                                                    style: {
                                                                                        fontSize: 20,
                                                                                        color: '#52c41a',
                                                                                        marginBottom: 4
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                    lineNumber: 386,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 385,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 24,
                                                                                    fontWeight: 700,
                                                                                    color: '#52c41a',
                                                                                    lineHeight: 1,
                                                                                    marginBottom: 4
                                                                                },
                                                                                children: personalStats.personnel
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 394,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 12,
                                                                                    color: '#52c41a',
                                                                                    fontWeight: 600,
                                                                                    opacity: 0.8
                                                                                },
                                                                                children: "人员"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 405,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 360,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 359,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                    xs: 12,
                                                                    sm: 12,
                                                                    md: 12,
                                                                    lg: 12,
                                                                    xl: 12,
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                        size: "small",
                                                                        style: {
                                                                            borderRadius: 12,
                                                                            border: 'none',
                                                                            background: 'linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%)',
                                                                            boxShadow: '0 4px 12px rgba(250, 173, 20, 0.15)',
                                                                            transition: 'all 0.3s ease',
                                                                            cursor: 'pointer'
                                                                        },
                                                                        styles: {
                                                                            body: {
                                                                                padding: '16px 12px',
                                                                                textAlign: 'center'
                                                                            }
                                                                        },
                                                                        onMouseEnter: (e)=>{
                                                                            e.currentTarget.style.transform = 'translateY(-2px)';
                                                                            e.currentTarget.style.boxShadow = '0 8px 24px rgba(250, 173, 20, 0.25)';
                                                                        },
                                                                        onMouseLeave: (e)=>{
                                                                            e.currentTarget.style.transform = 'translateY(0)';
                                                                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(250, 173, 20, 0.15)';
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    marginBottom: 8
                                                                                },
                                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                                                    style: {
                                                                                        fontSize: 20,
                                                                                        color: '#faad14',
                                                                                        marginBottom: 4
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                    lineNumber: 446,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 445,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 24,
                                                                                    fontWeight: 700,
                                                                                    color: '#faad14',
                                                                                    lineHeight: 1,
                                                                                    marginBottom: 4
                                                                                },
                                                                                children: personalStats.warnings
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 454,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 12,
                                                                                    color: '#faad14',
                                                                                    fontWeight: 600,
                                                                                    opacity: 0.8
                                                                                },
                                                                                children: "预警"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 465,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 420,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 419,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                                    xs: 12,
                                                                    sm: 12,
                                                                    md: 12,
                                                                    lg: 12,
                                                                    xl: 12,
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                                        size: "small",
                                                                        style: {
                                                                            borderRadius: 12,
                                                                            border: 'none',
                                                                            background: 'linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%)',
                                                                            boxShadow: '0 4px 12px rgba(255, 77, 79, 0.15)',
                                                                            transition: 'all 0.3s ease',
                                                                            cursor: 'pointer'
                                                                        },
                                                                        styles: {
                                                                            body: {
                                                                                padding: '16px 12px',
                                                                                textAlign: 'center'
                                                                            }
                                                                        },
                                                                        onMouseEnter: (e)=>{
                                                                            e.currentTarget.style.transform = 'translateY(-2px)';
                                                                            e.currentTarget.style.boxShadow = '0 8px 24px rgba(255, 77, 79, 0.25)';
                                                                        },
                                                                        onMouseLeave: (e)=>{
                                                                            e.currentTarget.style.transform = 'translateY(0)';
                                                                            e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 77, 79, 0.15)';
                                                                        },
                                                                        children: [
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    marginBottom: 8
                                                                                },
                                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.AlertOutlined, {
                                                                                    style: {
                                                                                        fontSize: 20,
                                                                                        color: '#ff4d4f',
                                                                                        marginBottom: 4
                                                                                    }
                                                                                }, void 0, false, {
                                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                    lineNumber: 506,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 505,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 24,
                                                                                    fontWeight: 700,
                                                                                    color: '#ff4d4f',
                                                                                    lineHeight: 1,
                                                                                    marginBottom: 4
                                                                                },
                                                                                children: personalStats.alerts
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 514,
                                                                                columnNumber: 27
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                                style: {
                                                                                    fontSize: 12,
                                                                                    color: '#ff4d4f',
                                                                                    fontWeight: 600,
                                                                                    opacity: 0.8
                                                                                },
                                                                                children: "告警"
                                                                            }, void 0, false, {
                                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                                lineNumber: 525,
                                                                                columnNumber: 27
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 480,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 479,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 312,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 308,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 295,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 153,
                                        columnNumber: 13
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                    lineNumber: 151,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 126,
                            columnNumber: 7
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UnifiedSettingsModal.default, {
                            visible: settingsModalVisible,
                            onCancel: ()=>setSettingsModalVisible(false),
                            userInfo: userInfo,
                            onSuccess: ()=>{
                                // 可以在这里刷新用户信息或团队列表
                                console.log('设置操作成功');
                            }
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 547,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                    lineNumber: 113,
                    columnNumber: 5
                }, this);
            };
            _s(PersonalInfo, "IzjvzsvZ1ZoM5sGldeIs9cfa/44=");
            _c = PersonalInfo;
            var _default = PersonalInfo;
            var _c;
            $RefreshReg$(_c, "PersonalInfo");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '13837029274183722557';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/settings/index.tsx": [
            "p__settings__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_personal-center_index_tsx-async.316150880838972929.hot-update.js.map