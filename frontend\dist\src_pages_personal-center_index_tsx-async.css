.todoContainer-a1WFEvHe {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  -webkit-animation: fadeInUp 0.6s ease-out;
  animation: fadeInUp-HHrm-h9D 0.6s ease-out;
}
.todoContainer-a1WFEvHe::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}
.decorativeElement-KP0GUaPn {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  -webkit-animation: float 6s ease-in-out infinite;
  animation: float-L5AuqKlR 6s ease-in-out infinite;
}
.decorativeElement-KP0GUaPn:nth-child(1) {
  top: -50px;
  right: -50px;
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}
.decorativeElement-KP0GUaPn:nth-child(2) {
  bottom: -30px;
  left: -30px;
  -webkit-animation-delay: 2s;
  animation-delay: 2s;
  width: 60px;
  height: 60px;
}
.todoTitle-Q8Yd77wc {
  color: white !important;
  font-size: 24px !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.todoSubtitle-R7EXOu5m {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 14px !important;
  margin-bottom: 24px !important;
}
.statsContainer-_pCEEkmq {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}
.statCard-DI-f7twj {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.statCard-DI-f7twj::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}
.statCard-DI-f7twj:hover::before {
  left: 100%;
}
.statCard-DI-f7twj:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}
.statValue-0oYTGaqy {
  font-size: 28px;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 4px;
}
.statLabel-Pula1_rR {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}
.progressContainer-1yc2Bs8i {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.progressLabel-ukZPe9Nl {
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}
.actionContainer-6zXyWbtT {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.searchInput-NBkWQ_IU {
  border-radius: 8px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
}
.searchInput-NBkWQ_IU input {
  background: transparent !important;
  color: white !important;
}
.searchInput-NBkWQ_IU input::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}
.searchInput-NBkWQ_IU input::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}
.addButton-whBzfbMx {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3) !important;
  transition: all 0.3s ease !important;
}
.addButton-whBzfbMx:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4) !important;
}
.todoTabs-CGGTaFdw {
  margin-bottom: 16px;
}
.todoTabs-CGGTaFdw .ant-tabs-tab-9R1Sbdwh {
  color: rgba(255, 255, 255, 0.7) !important;
  font-weight: 500;
}
.todoTabs-CGGTaFdw .ant-tabs-tab-active-8tdgC9ZQ {
  color: white !important;
}
.todoTabs-CGGTaFdw .ant-tabs-ink-bar-zL77fKlk {
  background: white !important;
}
.todoListContainer-jdwHXGgT {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 400px;
}
.todoItem-dQVYNQpE {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  -webkit-animation: slideInUp 0.3s ease-out;
  animation: slideInUp-hWo1cFvD 0.3s ease-out;
}
.todoItem-dQVYNQpE::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: all 0.3s ease;
}
.todoItem-dQVYNQpE.highPriority-RbP170ri::before {
  background: linear-gradient(180deg, #ff4d4f 0%, #ff7875 100%);
}
.todoItem-dQVYNQpE.mediumPriority-3rnhC8lN::before {
  background: linear-gradient(180deg, #faad14 0%, #ffc53d 100%);
}
.todoItem-dQVYNQpE.lowPriority-GntA1MAy::before {
  background: linear-gradient(180deg, #8c8c8c 0%, #bfbfbf 100%);
}
.todoItem-dQVYNQpE.completed-BhlstMsA::before {
  background: linear-gradient(180deg, #52c41a 0%, #73d13d 100%);
}
.todoItem-dQVYNQpE:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}
.todoItem-dQVYNQpE.completed-BhlstMsA {
  opacity: 0.7;
}
.statusIndicator-Ee2e6bQB {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}
.statusIndicator-Ee2e6bQB.completed-BhlstMsA {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
}
.statusIndicator-Ee2e6bQB.pending-40gf38Uu {
  border: 2px solid;
  background: white;
}
.statusIndicator-Ee2e6bQB.pending-40gf38Uu.highPriority-RbP170ri {
  border-color: #ff4d4f;
}
.statusIndicator-Ee2e6bQB.pending-40gf38Uu.mediumPriority-3rnhC8lN {
  border-color: #faad14;
}
.statusIndicator-Ee2e6bQB.pending-40gf38Uu.lowPriority-GntA1MAy {
  border-color: #8c8c8c;
}
.statusIndicator-Ee2e6bQB:hover {
  transform: scale(1.1);
}
.todoContent-LGGo3u1D {
  flex: 1 1;
  margin-left: 12px;
}
.todoTitle-Q8Yd77wc {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.4;
}
.todoDescription-o4gIZ9Vg {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
  line-height: 1.5;
}
.todoMeta-on56osN1 {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #bfbfbf;
}
.todoActions-wqilqyD2 {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.todoItem-dQVYNQpE:hover .todoActions-wqilqyD2 {
  opacity: 1;
}
.actionButton-NVATJu4g {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.04);
}
.actionButton-NVATJu4g:hover {
  transform: scale(1.1);
}
.actionButton-NVATJu4g.edit-Y-2JkI3B {
  color: #1890ff;
}
.actionButton-NVATJu4g.edit-Y-2JkI3B:hover {
  background: rgba(24, 144, 255, 0.1);
}
.actionButton-NVATJu4g.delete-pGgIrCQU {
  color: #ff4d4f;
}
.actionButton-NVATJu4g.delete-pGgIrCQU:hover {
  background: rgba(255, 77, 79, 0.1);
}
.priorityBadge-Z_0wEmtu {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.priorityBadge-Z_0wEmtu.high-YZ-0v4hQ {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border: 1px solid rgba(255, 77, 79, 0.2);
}
.priorityBadge-Z_0wEmtu.medium-XLC3UvPa {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
  border: 1px solid rgba(250, 173, 20, 0.2);
}
.priorityBadge-Z_0wEmtu.low-d1395bxg {
  background: rgba(140, 140, 140, 0.1);
  color: #8c8c8c;
  border: 1px solid rgba(140, 140, 140, 0.2);
}
@keyframes fadeInUp-HHrm-h9D {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slideInUp-hWo1cFvD {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes float-L5AuqKlR {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
@keyframes pulse-o5d59dLw {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
@media (max-width: 768px) {
  .todoContainer-a1WFEvHe {
    padding: 16px;
    border-radius: 12px;
  }
  .statsContainer-_pCEEkmq {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  .statCard-DI-f7twj {
    padding: 12px;
  }
  .statValue-0oYTGaqy {
    font-size: 24px;
  }
  .todoItem-dQVYNQpE {
    padding: 12px;
  }
  .todoTitle-Q8Yd77wc {
    font-size: 20px !important;
  }
}
@media (max-width: 480px) {
  .statsContainer-_pCEEkmq {
    grid-template-columns: 1fr;
  }
  .actionContainer-6zXyWbtT {
    padding: 12px;
  }
  .todoActions-wqilqyD2 {
    opacity: 1;
  }
}
.personalInfoCard-GMTUvuoL {
  margin-bottom: 24px;
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
}
.personalInfoCard-GMTUvuoL:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}
.decorativeCircle1-HEEi2j9f {
  position: absolute;
  top: -50px;
  right: -50px;
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 1;
  -webkit-animation: float 6s ease-in-out infinite;
  animation: float-DAEgOouD 6s ease-in-out infinite;
}
.decorativeCircle2-t8HUdx07 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  z-index: 1;
  animation: float-DAEgOouD 8s ease-in-out infinite reverse;
}
.contentArea-_n2x4BDb {
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  margin: 2px;
  border-radius: 14px;
  padding: 24px;
  transition: all 0.3s ease;
}
.titleBar-GUpHpaO- {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}
.title-OR5wN18v {
  margin: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 600;
}
.settingsButton-mrJVLRGb {
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #667eea;
  transition: all 0.3s ease;
}
.settingsButton-mrJVLRGb:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  transform: scale(1.1);
}
.avatarContainer-DSdWVJ9f {
  position: relative;
}
.avatar-_T1D7bf2 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  font-size: 28px;
  font-weight: 600;
  border: 4px solid rgba(102, 126, 234, 0.1);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}
.avatar-_T1D7bf2:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 32px rgba(102, 126, 234, 0.3);
}
.onlineIndicator-dZLd46X5 {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: #52c41a;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  -webkit-animation: pulse 2s infinite;
  animation: pulse-DmGLSG7j 2s infinite;
}
.contactCard-iYaWi3ca {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid;
  transition: all 0.3s ease;
}
.contactCard-iYaWi3ca:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.emailCard-iN8WyD69 {
  background: rgba(24, 144, 255, 0.05);
  border-color: rgba(24, 144, 255, 0.1);
}
.phoneCard-1wnvLyi2 {
  background: rgba(82, 196, 26, 0.05);
  border-color: rgba(82, 196, 26, 0.1);
}
.additionalInfo-SWj22vN7 {
  margin-top: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}
.additionalInfo-SWj22vN7:hover {
  background: #f5f5f5;
  border-color: #e0e0e0;
}
.statsCard-RbylBLDK {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  cursor: pointer;
}
.statsCard-RbylBLDK:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
}
@keyframes float-DAEgOouD {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}
@keyframes pulse-DmGLSG7j {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}
@keyframes fadeInUp-LmQACtmh {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@media (max-width: 768px) {
  .contentArea-_n2x4BDb {
    padding: 16px;
  }
  .titleBar-GUpHpaO- {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
  .avatar-_T1D7bf2 {
    font-size: 24px;
  }
  .contactCard-iYaWi3ca {
    padding: 6px 10px;
  }
  .additionalInfo-SWj22vN7 {
    margin-top: 16px;
    padding: 12px;
  }
}
@media (max-width: 576px) {
  .personalInfoCard-GMTUvuoL {
    margin-bottom: 16px;
    border-radius: 12px;
  }
  .contentArea-_n2x4BDb {
    padding: 12px;
    border-radius: 10px;
  }
  .decorativeCircle1-HEEi2j9f {
    width: 80px;
    height: 80px;
    top: -40px;
    right: -40px;
  }
  .decorativeCircle2-t8HUdx07 {
    width: 60px;
    height: 60px;
    bottom: -30px;
    left: -30px;
  }
}
.loadingContainer-lZKULeb8 {
  -webkit-animation: fadeInUp 0.6s ease-out;
  animation: fadeInUp-LmQACtmh 0.6s ease-out;
}
.statsCard-RbylBLDK:active {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s ease;
}
.contactCard-iYaWi3ca:active {
  transform: translateX(2px) scale(0.98);
  transition: all 0.1s ease;
}
.settingsButton-mrJVLRGb:active {
  transform: scale(0.95);
  transition: all 0.1s ease;
}
.avatar-_T1D7bf2:active {
  transform: scale(1.02);
  transition: all 0.1s ease;
}
.skeletonCard-fH7lshhh {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  -webkit-animation: shimmer 1.5s infinite;
  animation: shimmer-lCzub6k8 1.5s infinite;
  border-radius: 12px;
  height: 80px;
}
@keyframes shimmer-lCzub6k8 {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
.successAnimation-UGiJBscn {
  -webkit-animation: successPulse 0.6s ease-out;
  animation: successPulse-xvCPq0dm 0.6s ease-out;
}
@keyframes successPulse-xvCPq0dm {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}
.errorAnimation-SVfCef80 {
  -webkit-animation: errorShake 0.6s ease-out;
  animation: errorShake-RkVI4HkN 0.6s ease-out;
}
@keyframes errorShake-RkVI4HkN {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}
.fadeInDelay1-kMZ2-Q5v {
  -webkit-animation: fadeInUp 0.6s ease-out 0.1s both;
  animation: fadeInUp-LmQACtmh 0.6s ease-out 0.1s both;
}
.fadeInDelay2-ZracY-tQ {
  -webkit-animation: fadeInUp 0.6s ease-out 0.2s both;
  animation: fadeInUp-LmQACtmh 0.6s ease-out 0.2s both;
}
.fadeInDelay3-nbwSZP8A {
  -webkit-animation: fadeInUp 0.6s ease-out 0.3s both;
  animation: fadeInUp-LmQACtmh 0.6s ease-out 0.3s both;
}
.fadeInDelay4-TJToLvzm {
  -webkit-animation: fadeInUp 0.6s ease-out 0.4s both;
  animation: fadeInUp-LmQACtmh 0.6s ease-out 0.4s both;
}
.statsTitle-6Xh-RYy9 {
  margin: 0 0 16px 0;
  color: #595959;
  font-weight: 600;
}
.vehicleCard-yHcN_T3F {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}
.personnelCard-N-N9u1us {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.15);
}
.warningCard-eA6yGLZz {
  background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);
  box-shadow: 0 4px 12px rgba(250, 173, 20, 0.15);
}
.alertCard-trqfzYSm {
  background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.15);
}
/*# sourceMappingURL=src_pages_personal-center_index_tsx-async.css.map*/