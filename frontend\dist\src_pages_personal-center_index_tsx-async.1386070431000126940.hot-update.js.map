{"version": 3, "sources": ["src_pages_personal-center_index_tsx-async.1386070431000126940.hot-update.js", "src/pages/personal-center/TodoManagement.tsx", "F:/Project/teamAuth/frontend/src/pages/personal-center/TodoManagement.module.css?asmodule"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/personal-center/index.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='4235359472720843180';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/settings/index.tsx\":[\"p__settings__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "import {\n  CalendarOutlined,\n  CheckOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  MoreOutlined,\n  PlusOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport {\n  <PERSON><PERSON>,\n  Button,\n  Card,\n  Col,\n  Dropdown,\n  Flex,\n  Form,\n  Input,\n  Progress,\n  Row,\n  Select,\n  Space,\n  Spin,\n  Tabs,\n  Tooltip,\n  Typography,\n} from 'antd';\nimport { ModalForm, ProList, ProCard } from '@ant-design/pro-components';\nimport React, { useEffect, useState } from 'react';\nimport { TodoService } from '@/services/todo';\nimport type { TodoResponse, TodoStatsResponse } from '@/types/api';\nimport styles from './TodoManagement.module.css';\n\nconst { Text } = Typography;\nconst { TabPane } = Tabs;\n\n// 使用API类型定义，不需要重复定义接口\ninterface TodoManagementProps {\n  onAddTodo?: (todo: TodoResponse) => void;\n  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;\n  onDeleteTodo?: (id: number) => void;\n}\n\nconst TodoManagement: React.FC<TodoManagementProps> = () => {\n  // TODO数据状态管理\n  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);\n  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({\n    highPriorityCount: 0,\n    mediumPriorityCount: 0,\n    lowPriorityCount: 0,\n    totalCount: 0,\n    completedCount: 0,\n    completionPercentage: 0,\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 待办事项状态管理\n  const [todoModalVisible, setTodoModalVisible] = useState(false);\n  const [todoForm] = Form.useForm();\n  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);\n\n  // 过滤器状态\n  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'completed'>(\n    'pending',\n  );\n  const [searchText, setSearchText] = useState('');\n\n  // 获取TODO数据\n  useEffect(() => {\n    const fetchTodoData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        console.log('TodoManagement: 开始获取TODO数据');\n\n        // 分别获取TODO列表和统计数据，避免一个失败影响另一个\n        const todosPromise = TodoService.getUserTodos().catch((error) => {\n          console.error('获取TODO列表失败:', error);\n          return [];\n        });\n\n        const statsPromise = TodoService.getTodoStats().catch((error) => {\n          console.error('获取TODO统计失败:', error);\n          return {\n            highPriorityCount: 0,\n            mediumPriorityCount: 0,\n            lowPriorityCount: 0,\n            totalCount: 0,\n            completedCount: 0,\n            completionPercentage: 0,\n          };\n        });\n\n        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);\n\n        console.log('TodoManagement: 获取到TODO列表:', todos);\n        console.log('TodoManagement: 获取到统计数据:', stats);\n\n        setPersonalTasks(todos);\n        setTodoStats(stats);\n      } catch (error) {\n        console.error('获取TODO数据时发生未知错误:', error);\n        setError('获取TODO数据失败，请刷新页面重试');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTodoData();\n  }, []);\n\n  // 根据激活的标签和搜索文本过滤任务\n  const filteredPersonalTasks = (personalTasks || []).filter((task) => {\n    // 根据标签过滤\n    if (activeTab === 'pending' && task.status === 1) return false;\n    if (activeTab === 'completed' && task.status === 0) return false;\n\n    // 根据搜索文本过滤\n    if (\n      searchText &&\n      !task.title.toLowerCase().includes(searchText.toLowerCase())\n    ) {\n      return false;\n    }\n\n    return true;\n  });\n\n  // 处理待办事项操作\n  const handleToggleTodoStatus = async (id: number) => {\n    try {\n      const task = personalTasks.find((t) => t.id === id);\n      if (!task) {\n        return;\n      }\n\n      const newStatus = task.status === 0 ? 1 : 0;\n\n      await TodoService.updateTodo(id, { status: newStatus });\n\n      // 更新本地状态\n      setPersonalTasks(\n        personalTasks.map((task) =>\n          task.id === id ? { ...task, status: newStatus } : task,\n        ),\n      );\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleAddOrUpdateTodo = async (values: any) => {\n    try {\n      if (editingTodoId) {\n        // 更新现有待办事项\n        const updatedTodo = await TodoService.updateTodo(editingTodoId, {\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks(\n          personalTasks.map((task) =>\n            task.id === editingTodoId ? updatedTodo : task,\n          ),\n        );\n      } else {\n        // 添加新待办事项\n        const newTodo = await TodoService.createTodo({\n          title: values.name,\n          priority: values.priority,\n        });\n\n        setPersonalTasks([newTodo, ...personalTasks]);\n      }\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n\n      // 重置表单并关闭模态框\n      setTodoModalVisible(false);\n      setEditingTodoId(null);\n      todoForm.resetFields();\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  const handleDeleteTodo = async (id: number) => {\n    try {\n      await TodoService.deleteTodo(id);\n      setPersonalTasks(personalTasks.filter((task) => task.id !== id));\n\n      // 刷新统计数据\n      try {\n        const stats = await TodoService.getTodoStats();\n        setTodoStats(stats);\n      } catch (statsError) {\n        // 统计数据刷新失败不影响主要操作\n      }\n    } catch (error) {\n      // 错误处理由响应拦截器统一处理\n    }\n  };\n\n  return (\n    <Card className={styles.todoContainer}>\n      {/* 装饰元素 */}\n      <div className={styles.decorativeElement}></div>\n      <div className={styles.decorativeElement}></div>\n\n      {/* 标题区域 */}\n      <div style={{ position: 'relative', zIndex: 1, marginBottom: 24 }}>\n        <Typography.Title level={3} className={styles.todoTitle}>\n          待办事项管理\n        </Typography.Title>\n        <Typography.Text className={styles.todoSubtitle}>\n          高效管理您的任务，提升工作效率\n        </Typography.Text>\n      </div>\n      {/* 统计信息区域 */}\n      <div className={styles.statsContainer} style={{ position: 'relative', zIndex: 1 }}>\n        <div className={styles.statCard}>\n          <div className={styles.statValue}>{todoStats.totalCount}</div>\n          <div className={styles.statLabel}>总任务</div>\n        </div>\n        <div className={styles.statCard}>\n          <div className={styles.statValue}>{todoStats.completedCount}</div>\n          <div className={styles.statLabel}>已完成</div>\n        </div>\n        <div className={styles.statCard}>\n          <div className={styles.statValue}>{todoStats.highPriorityCount}</div>\n          <div className={styles.statLabel}>高优先级</div>\n        </div>\n        <div className={styles.statCard}>\n          <div className={styles.statValue}>{todoStats.mediumPriorityCount}</div>\n          <div className={styles.statLabel}>中优先级</div>\n        </div>\n        <div className={styles.statCard}>\n          <div className={styles.statValue}>{todoStats.lowPriorityCount}</div>\n          <div className={styles.statLabel}>低优先级</div>\n        </div>\n      </div>\n\n      {/* 进度条区域 */}\n      <div className={styles.progressContainer} style={{ position: 'relative', zIndex: 1 }}>\n        <div className={styles.progressLabel}>\n          任务完成进度 {todoStats.completedCount}/{todoStats.totalCount}\n        </div>\n        <Progress\n          percent={todoStats.completionPercentage}\n          strokeColor={{\n            '0%': '#4facfe',\n            '100%': '#00f2fe',\n          }}\n          trailColor=\"rgba(255, 255, 255, 0.2)\"\n          showInfo={false}\n          strokeWidth={8}\n        />\n        <div style={{ textAlign: 'right', marginTop: 8 }}>\n          <span style={{ color: 'white', fontSize: 16, fontWeight: 600 }}>\n            {todoStats.completionPercentage}%\n          </span>\n        </div>\n      </div>\n\n      {/* 操作区域 */}\n      <div className={styles.actionContainer} style={{ position: 'relative', zIndex: 1 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={16} md={18} lg={20}>\n            <Input.Search\n              placeholder=\"搜索任务...\"\n              allowClear\n              prefix={<SearchOutlined />}\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              className={styles.searchInput}\n              size=\"large\"\n            />\n          </Col>\n          <Col xs={24} sm={8} md={6} lg={4}>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              className={styles.addButton}\n              size=\"large\"\n              block\n              onClick={() => {\n                setEditingTodoId(null);\n                todoForm.resetFields();\n                setTodoModalVisible(true);\n              }}\n            >\n              添加新任务\n            </Button>\n          </Col>\n        </Row>\n      </div>\n\n      {/* 标签页 */}\n      <Tabs\n        activeKey={activeTab}\n        onChange={(key) => setActiveTab(key as 'all' | 'pending' | 'completed')}\n        size=\"large\"\n        className={styles.todoTabs}\n        style={{ position: 'relative', zIndex: 1, marginBottom: 16 }}\n      >\n        <TabPane tab=\"全部任务\" key=\"all\" />\n        <TabPane tab=\"待处理\" key=\"pending\" />\n        <TabPane tab=\"已完成\" key=\"completed\" />\n      </Tabs>\n\n      {/* 任务列表 */}\n      <div className={styles.todoListContainer} style={{ position: 'relative', zIndex: 1 }}>\n        {error ? (\n          <Alert\n            message=\"TODO数据加载失败\"\n            description={error}\n            type=\"error\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n        ) : (\n          <Spin spinning={loading}>\n            {filteredPersonalTasks.length === 0 ? (\n              <div style={{ textAlign: 'center', padding: '40px 0', color: 'rgba(255, 255, 255, 0.6)' }}>\n                <Typography.Text style={{ color: 'rgba(255, 255, 255, 0.6)' }}>\n                  暂无任务数据\n                </Typography.Text>\n              </div>\n            ) : (\n              filteredPersonalTasks.map((item) => {\n                const priorityClass = item.priority === 3 ? 'highPriority' :\n                                    item.priority === 2 ? 'mediumPriority' : 'lowPriority';\n                const statusClass = item.status === 1 ? 'completed' : 'pending';\n\n                return (\n                  <div\n                    key={item.id}\n                    className={`${styles.todoItem} ${styles[priorityClass]} ${item.status === 1 ? styles.completed : ''}`}\n                  >\n                    <Flex align=\"center\" gap={16} style={{ width: '100%' }}>\n                      {/* 状态指示器 */}\n                      <div\n                        className={`${styles.statusIndicator} ${styles[statusClass]} ${styles[priorityClass]}`}\n                        onClick={() => handleToggleTodoStatus(item.id, item.status === 1 ? 0 : 1)}\n                      >\n                        {item.status === 1 && (\n                          <CheckOutlined style={{ color: '#fff', fontSize: 12 }} />\n                        )}\n                      </div>\n\n                      {/* 任务内容 */}\n                      <div className={styles.todoContent}>\n                        <div className={styles.todoTitle} style={{\n                          textDecoration: item.status === 1 ? 'line-through' : 'none',\n                          color: item.status === 1 ? '#8c8c8c' : '#262626',\n                        }}>\n                          {item.title}\n                        </div>\n                        {item.description && (\n                          <div className={styles.todoDescription}>\n                            {item.description}\n                          </div>\n                        )}\n\n                        <div className={styles.todoMeta}>\n                          <CalendarOutlined />\n                          <span>创建于 {new Date(item.createdAt).toLocaleDateString('zh-CN')}</span>\n                          <span className={`${styles.priorityBadge} ${styles[priorityClass.replace('Priority', '')]}`}>\n                            {item.priority === 3 ? '高优先级' : item.priority === 2 ? '中优先级' : '低优先级'}\n                          </span>\n                        </div>\n                      </div>\n\n                      {/* 操作按钮 */}\n                      <div className={styles.todoActions}>\n                        <button\n                          className={`${styles.actionButton} ${styles.edit}`}\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            setEditingTodoId(item.id);\n                            todoForm.setFieldsValue({\n                              name: item.title,\n                              priority: item.priority,\n                            });\n                            setTodoModalVisible(true);\n                          }}\n                        >\n                          <EditOutlined />\n                        </button>\n                        <button\n                          className={`${styles.actionButton} ${styles.delete}`}\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            handleDeleteTodo(item.id);\n                          }}\n                        >\n                          <DeleteOutlined />\n                        </button>\n                      </div>\n                    </Flex>\n                  </div>\n                );\n              })\n            )}\n          </Spin>\n        )}\n      </div>\n      {/* 待办事项表单模态框 */}\n          <ModalForm\n            title={editingTodoId ? '编辑待办事项' : '新增待办事项'}\n            open={todoModalVisible}\n            onOpenChange={(visible) => {\n              setTodoModalVisible(visible);\n              if (!visible) {\n                setEditingTodoId(null);\n                todoForm.resetFields();\n              }\n            }}\n            form={todoForm}\n            layout=\"vertical\"\n            onFinish={handleAddOrUpdateTodo}\n            autoComplete=\"off\"\n            width={500}\n            modalProps={{\n              centered: true,\n              destroyOnClose: true,\n              maskClosable: true,\n              keyboard: true,\n              forceRender: false,\n            }}\n            submitter={{\n              searchConfig: {\n                submitText: editingTodoId ? '更新任务' : '创建任务',\n                resetText: '取消',\n              },\n              submitButtonProps: {\n                style: {\n                  background: '#1890ff',\n                  borderColor: '#1890ff',\n                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',\n                },\n                icon: editingTodoId ? <EditOutlined /> : <PlusOutlined />,\n              },\n              resetButtonProps: {\n                style: {\n                  borderColor: '#d9d9d9',\n                },\n              },\n              onReset: () => {\n                setTodoModalVisible(false);\n                setEditingTodoId(null);\n                todoForm.resetFields();\n              },\n            }}\n            preserve={false}\n          >\n            <Form.Item\n              name=\"name\"\n              label=\"任务名称\"\n              rules={[{ required: true, message: '请输入任务名称' }]}\n            >\n              <Input\n                placeholder=\"请输入任务名称\"\n                size=\"large\"\n                style={{ borderRadius: 6 }}\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"priority\"\n              label=\"优先级\"\n              initialValue={2}\n              rules={[{ required: true, message: '请选择优先级' }]}\n            >\n              <Select\n                size=\"large\"\n                options={[\n                  { value: 3, label: '高优先级' },\n                  { value: 2, label: '中优先级' },\n                  { value: 1, label: '低优先级' },\n                ]}\n                style={{ borderRadius: 6 }}\n              />\n            </Form.Item>\n          </ModalForm>\n    </Card>\n  );\n};\n\nexport default TodoManagement;\n", "\nimport \"F:/Project/teamAuth/frontend/src/pages/personal-center/TodoManagement.module.css?modules\";\nexport default {\"progressContainer\": `progressContainer-1yc2Bs8i`,\"actionContainer\": `actionContainer-6zXyWbtT`,\"todoActions\": `todoActions-wqilqyD2`,\"low\": `low-d1395bxg`,\"todoItem\": `todoItem-dQVYNQpE`,\"searchInput\": `searchInput-NBkWQ_IU`,\"completed\": `completed-BhlstMsA`,\"todoContainer\": `todoContainer-a1WFEvHe`,\"highPriority\": `highPriority-RbP170ri`,\"todoTitle\": `todoTitle-Q8Yd77wc`,\"progressLabel\": `progressLabel-ukZPe9Nl`,\"statsContainer\": `statsContainer-_pCEEkmq`,\"ant-tabs-tab-active\": `ant-tabs-tab-active-8tdgC9ZQ`,\"ant-tabs-ink-bar\": `ant-tabs-ink-bar-zL77fKlk`,\"lowPriority\": `lowPriority-GntA1MAy`,\"todoDescription\": `todoDescription-o4gIZ9Vg`,\"slideInUp\": `slideInUp-hWo1cFvD`,\"edit\": `edit-Y-2JkI3B`,\"priorityBadge\": `priorityBadge-Z_0wEmtu`,\"high\": `high-YZ-0v4hQ`,\"pending\": `pending-40gf38Uu`,\"fadeInUp\": `fadeInUp-HHrm-h9D`,\"float\": `float-L5AuqKlR`,\"statCard\": `statCard-DI-f7twj`,\"decorativeElement\": `decorativeElement-KP0GUaPn`,\"statValue\": `statValue-0oYTGaqy`,\"addButton\": `addButton-whBzfbMx`,\"todoSubtitle\": `todoSubtitle-R7EXOu5m`,\"statLabel\": `statLabel-Pula1_rR`,\"ant-tabs-tab\": `ant-tabs-tab-9R1Sbdwh`,\"todoListContainer\": `todoListContainer-jdwHXGgT`,\"todoContent\": `todoContent-LGGo3u1D`,\"pulse\": `pulse-o5d59dLw`,\"actionButton\": `actionButton-NVATJu4g`,\"statusIndicator\": `statusIndicator-Ee2e6bQB`,\"delete\": `delete-pGgIrCQU`,\"medium\": `medium-XLC3UvPa`,\"todoTabs\": `todoTabs-CGGTaFdw`,\"mediumPriority\": `mediumPriority-3rnhC8lN`,\"todoMeta\": `todoMeta-on56osN1`}\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uCACA;IACE,SAAS;;;;;;wCCsfb;;;2BAAA;;;;;;;0CAjfO;yCAkBA;kDACqC;oFACD;yCACf;6GAET;;;;;;;;;;YAEnB,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAU;YAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,UAAI;YASxB,MAAM,iBAAgD;;gBACpD,aAAa;gBACb,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAiB,EAAE;gBACrE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAoB;oBAC5D,mBAAmB;oBACnB,qBAAqB;oBACrB,kBAAkB;oBAClB,YAAY;oBACZ,gBAAgB;oBAChB,sBAAsB;gBACxB;gBACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;gBACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,eAAQ,EAAgB;gBAElD,WAAW;gBACX,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,eAAQ,EAAC;gBACzD,MAAM,CAAC,SAAS,GAAG,UAAI,CAAC,OAAO;gBAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,eAAQ,EAAgB;gBAElE,QAAQ;gBACR,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EACxC;gBAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;gBAE7C,WAAW;gBACX,IAAA,gBAAS,EAAC;oBACR,MAAM,gBAAgB;wBACpB,IAAI;4BACF,WAAW;4BACX,SAAS;4BAET,QAAQ,GAAG,CAAC;4BAEZ,8BAA8B;4BAC9B,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;gCACrD,QAAQ,KAAK,CAAC,eAAe;gCAC7B,OAAO,EAAE;4BACX;4BAEA,MAAM,eAAe,iBAAW,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;gCACrD,QAAQ,KAAK,CAAC,eAAe;gCAC7B,OAAO;oCACL,mBAAmB;oCACnB,qBAAqB;oCACrB,kBAAkB;oCAClB,YAAY;oCACZ,gBAAgB;oCAChB,sBAAsB;gCACxB;4BACF;4BAEA,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gCAAC;gCAAc;6BAAa;4BAErE,QAAQ,GAAG,CAAC,8BAA8B;4BAC1C,QAAQ,GAAG,CAAC,4BAA4B;4BAExC,iBAAiB;4BACjB,aAAa;wBACf,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,oBAAoB;4BAClC,SAAS;wBACX,SAAU;4BACR,WAAW;wBACb;oBACF;oBAEA;gBACF,GAAG,EAAE;gBAEL,mBAAmB;gBACnB,MAAM,wBAAwB,AAAC,CAAA,iBAAiB,EAAE,AAAD,EAAG,MAAM,CAAC,CAAC;oBAC1D,SAAS;oBACT,IAAI,cAAc,aAAa,KAAK,MAAM,KAAK,GAAG,OAAO;oBACzD,IAAI,cAAc,eAAe,KAAK,MAAM,KAAK,GAAG,OAAO;oBAE3D,WAAW;oBACX,IACE,cACA,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEzD,OAAO;oBAGT,OAAO;gBACT;gBAEA,WAAW;gBACX,MAAM,yBAAyB,OAAO;oBACpC,IAAI;wBACF,MAAM,OAAO,cAAc,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;wBAChD,IAAI,CAAC,MACH;wBAGF,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,IAAI;wBAE1C,MAAM,iBAAW,CAAC,UAAU,CAAC,IAAI;4BAAE,QAAQ;wBAAU;wBAErD,SAAS;wBACT,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,KAAK;gCAAE,GAAG,IAAI;gCAAE,QAAQ;4BAAU,IAAI;wBAItD,SAAS;wBACT,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;4BAC5C,aAAa;wBACf,EAAE,OAAO,YAAY;wBACnB,kBAAkB;wBACpB;oBACF,EAAE,OAAO,OAAO;oBACd,iBAAiB;oBACnB;gBACF;gBAEA,MAAM,wBAAwB,OAAO;oBACnC,IAAI;wBACF,IAAI,eAAe;4BACjB,WAAW;4BACX,MAAM,cAAc,MAAM,iBAAW,CAAC,UAAU,CAAC,eAAe;gCAC9D,OAAO,OAAO,IAAI;gCAClB,UAAU,OAAO,QAAQ;4BAC3B;4BAEA,iBACE,cAAc,GAAG,CAAC,CAAC,OACjB,KAAK,EAAE,KAAK,gBAAgB,cAAc;wBAGhD,OAAO;4BACL,UAAU;4BACV,MAAM,UAAU,MAAM,iBAAW,CAAC,UAAU,CAAC;gCAC3C,OAAO,OAAO,IAAI;gCAClB,UAAU,OAAO,QAAQ;4BAC3B;4BAEA,iBAAiB;gCAAC;mCAAY;6BAAc;wBAC9C;wBAEA,SAAS;wBACT,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;4BAC5C,aAAa;wBACf,EAAE,OAAO,YAAY;wBACnB,kBAAkB;wBACpB;wBAEA,aAAa;wBACb,oBAAoB;wBACpB,iBAAiB;wBACjB,SAAS,WAAW;oBACtB,EAAE,OAAO,OAAO;oBACd,iBAAiB;oBACnB;gBACF;gBAEA,MAAM,mBAAmB,OAAO;oBAC9B,IAAI;wBACF,MAAM,iBAAW,CAAC,UAAU,CAAC;wBAC7B,iBAAiB,cAAc,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;wBAE5D,SAAS;wBACT,IAAI;4BACF,MAAM,QAAQ,MAAM,iBAAW,CAAC,YAAY;4BAC5C,aAAa;wBACf,EAAE,OAAO,YAAY;wBACnB,kBAAkB;wBACpB;oBACF,EAAE,OAAO,OAAO;oBACd,iBAAiB;oBACnB;gBACF;gBAEA,qBACE,2BAAC,UAAI;oBAAC,WAAW,wCAAM,CAAC,aAAa;;sCAEnC,2BAAC;4BAAI,WAAW,wCAAM,CAAC,iBAAiB;;;;;;sCACxC,2BAAC;4BAAI,WAAW,wCAAM,CAAC,iBAAiB;;;;;;sCAGxC,2BAAC;4BAAI,OAAO;gCAAE,UAAU;gCAAY,QAAQ;gCAAG,cAAc;4BAAG;;8CAC9D,2BAAC,gBAAU,CAAC,KAAK;oCAAC,OAAO;oCAAG,WAAW,wCAAM,CAAC,SAAS;8CAAE;;;;;;8CAGzD,2BAAC,gBAAU,CAAC,IAAI;oCAAC,WAAW,wCAAM,CAAC,YAAY;8CAAE;;;;;;;;;;;;sCAKnD,2BAAC;4BAAI,WAAW,wCAAM,CAAC,cAAc;4BAAE,OAAO;gCAAE,UAAU;gCAAY,QAAQ;4BAAE;;8CAC9E,2BAAC;oCAAI,WAAW,wCAAM,CAAC,QAAQ;;sDAC7B,2BAAC;4CAAI,WAAW,wCAAM,CAAC,SAAS;sDAAG,UAAU,UAAU;;;;;;sDACvD,2BAAC;4CAAI,WAAW,wCAAM,CAAC,SAAS;sDAAE;;;;;;;;;;;;8CAEpC,2BAAC;oCAAI,WAAW,wCAAM,CAAC,QAAQ;;sDAC7B,2BAAC;4CAAI,WAAW,wCAAM,CAAC,SAAS;sDAAG,UAAU,cAAc;;;;;;sDAC3D,2BAAC;4CAAI,WAAW,wCAAM,CAAC,SAAS;sDAAE;;;;;;;;;;;;8CAEpC,2BAAC;oCAAI,WAAW,wCAAM,CAAC,QAAQ;;sDAC7B,2BAAC;4CAAI,WAAW,wCAAM,CAAC,SAAS;sDAAG,UAAU,iBAAiB;;;;;;sDAC9D,2BAAC;4CAAI,WAAW,wCAAM,CAAC,SAAS;sDAAE;;;;;;;;;;;;8CAEpC,2BAAC;oCAAI,WAAW,wCAAM,CAAC,QAAQ;;sDAC7B,2BAAC;4CAAI,WAAW,wCAAM,CAAC,SAAS;sDAAG,UAAU,mBAAmB;;;;;;sDAChE,2BAAC;4CAAI,WAAW,wCAAM,CAAC,SAAS;sDAAE;;;;;;;;;;;;8CAEpC,2BAAC;oCAAI,WAAW,wCAAM,CAAC,QAAQ;;sDAC7B,2BAAC;4CAAI,WAAW,wCAAM,CAAC,SAAS;sDAAG,UAAU,gBAAgB;;;;;;sDAC7D,2BAAC;4CAAI,WAAW,wCAAM,CAAC,SAAS;sDAAE;;;;;;;;;;;;;;;;;;sCAKtC,2BAAC;4BAAI,WAAW,wCAAM,CAAC,iBAAiB;4BAAE,OAAO;gCAAE,UAAU;gCAAY,QAAQ;4BAAE;;8CACjF,2BAAC;oCAAI,WAAW,wCAAM,CAAC,aAAa;;wCAAE;wCAC5B,UAAU,cAAc;wCAAC;wCAAE,UAAU,UAAU;;;;;;;8CAEzD,2BAAC,cAAQ;oCACP,SAAS,UAAU,oBAAoB;oCACvC,aAAa;wCACX,MAAM;wCACN,QAAQ;oCACV;oCACA,YAAW;oCACX,UAAU;oCACV,aAAa;;;;;;8CAEf,2BAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAS,WAAW;oCAAE;8CAC7C,cAAA,2BAAC;wCAAK,OAAO;4CAAE,OAAO;4CAAS,UAAU;4CAAI,YAAY;wCAAI;;4CAC1D,UAAU,oBAAoB;4CAAC;;;;;;;;;;;;;;;;;;sCAMtC,2BAAC;4BAAI,WAAW,wCAAM,CAAC,eAAe;4BAAE,OAAO;gCAAE,UAAU;gCAAY,QAAQ;4BAAE;sCAC/E,cAAA,2BAAC,SAAG;gCAAC,QAAQ;oCAAC;oCAAI;iCAAG;;kDACnB,2BAAC,SAAG;wCAAC,IAAI;wCAAI,IAAI;wCAAI,IAAI;wCAAI,IAAI;kDAC/B,cAAA,2BAAC,WAAK,CAAC,MAAM;4CACX,aAAY;4CACZ,UAAU;4CACV,sBAAQ,2BAAC,qBAAc;;;;;4CACvB,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAW,wCAAM,CAAC,WAAW;4CAC7B,MAAK;;;;;;;;;;;kDAGT,2BAAC,SAAG;wCAAC,IAAI;wCAAI,IAAI;wCAAG,IAAI;wCAAG,IAAI;kDAC7B,cAAA,2BAAC,YAAM;4CACL,MAAK;4CACL,oBAAM,2BAAC,mBAAY;;;;;4CACnB,WAAW,wCAAM,CAAC,SAAS;4CAC3B,MAAK;4CACL,KAAK;4CACL,SAAS;gDACP,iBAAiB;gDACjB,SAAS,WAAW;gDACpB,oBAAoB;4CACtB;sDACD;;;;;;;;;;;;;;;;;;;;;;sCAQP,2BAAC,UAAI;4BACH,WAAW;4BACX,UAAU,CAAC,MAAQ,aAAa;4BAChC,MAAK;4BACL,WAAW,wCAAM,CAAC,QAAQ;4BAC1B,OAAO;gCAAE,UAAU;gCAAY,QAAQ;gCAAG,cAAc;4BAAG;;8CAE3D,2BAAC;oCAAQ,KAAI;mCAAW;;;;;8CACxB,2BAAC;oCAAQ,KAAI;mCAAU;;;;;8CACvB,2BAAC;oCAAQ,KAAI;mCAAU;;;;;;;;;;;sCAIzB,2BAAC;4BAAI,WAAW,wCAAM,CAAC,iBAAiB;4BAAE,OAAO;gCAAE,UAAU;gCAAY,QAAQ;4BAAE;sCAChF,sBACC,2BAAC,WAAK;gCACJ,SAAQ;gCACR,aAAa;gCACb,MAAK;gCACL,QAAQ;gCACR,OAAO;oCAAE,cAAc;gCAAG;;;;;qDAG5B,2BAAC,UAAI;gCAAC,UAAU;0CACb,sBAAsB,MAAM,KAAK,kBAChC,2BAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAU,SAAS;wCAAU,OAAO;oCAA2B;8CACtF,cAAA,2BAAC,gBAAU,CAAC,IAAI;wCAAC,OAAO;4CAAE,OAAO;wCAA2B;kDAAG;;;;;;;;;;2CAKjE,sBAAsB,GAAG,CAAC,CAAC;oCACzB,MAAM,gBAAgB,KAAK,QAAQ,KAAK,IAAI,iBACxB,KAAK,QAAQ,KAAK,IAAI,mBAAmB;oCAC7D,MAAM,cAAc,KAAK,MAAM,KAAK,IAAI,cAAc;oCAEtD,qBACE,2BAAC;wCAEC,WAAW,CAAC,EAAE,wCAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,wCAAM,CAAC,cAAc,CAAC,CAAC,EAAE,KAAK,MAAM,KAAK,IAAI,wCAAM,CAAC,SAAS,GAAG,GAAG,CAAC;kDAErG,cAAA,2BAAC,UAAI;4CAAC,OAAM;4CAAS,KAAK;4CAAI,OAAO;gDAAE,OAAO;4CAAO;;8DAEnD,2BAAC;oDACC,WAAW,CAAC,EAAE,wCAAM,CAAC,eAAe,CAAC,CAAC,EAAE,wCAAM,CAAC,YAAY,CAAC,CAAC,EAAE,wCAAM,CAAC,cAAc,CAAC,CAAC;oDACtF,SAAS,IAAM,uBAAuB,KAAK,EAAE,EAAE,KAAK,MAAM,KAAK,IAAI,IAAI;8DAEtE,KAAK,MAAM,KAAK,mBACf,2BAAC,oBAAa;wDAAC,OAAO;4DAAE,OAAO;4DAAQ,UAAU;wDAAG;;;;;;;;;;;8DAKxD,2BAAC;oDAAI,WAAW,wCAAM,CAAC,WAAW;;sEAChC,2BAAC;4DAAI,WAAW,wCAAM,CAAC,SAAS;4DAAE,OAAO;gEACvC,gBAAgB,KAAK,MAAM,KAAK,IAAI,iBAAiB;gEACrD,OAAO,KAAK,MAAM,KAAK,IAAI,YAAY;4DACzC;sEACG,KAAK,KAAK;;;;;;wDAEZ,KAAK,WAAW,kBACf,2BAAC;4DAAI,WAAW,wCAAM,CAAC,eAAe;sEACnC,KAAK,WAAW;;;;;;sEAIrB,2BAAC;4DAAI,WAAW,wCAAM,CAAC,QAAQ;;8EAC7B,2BAAC,uBAAgB;;;;;8EACjB,2BAAC;;wEAAK;wEAAK,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC;;;;;;;8EACvD,2BAAC;oEAAK,WAAW,CAAC,EAAE,wCAAM,CAAC,aAAa,CAAC,CAAC,EAAE,wCAAM,CAAC,cAAc,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;8EACxF,KAAK,QAAQ,KAAK,IAAI,SAAS,KAAK,QAAQ,KAAK,IAAI,SAAS;;;;;;;;;;;;;;;;;;8DAMrE,2BAAC;oDAAI,WAAW,wCAAM,CAAC,WAAW;;sEAChC,2BAAC;4DACC,WAAW,CAAC,EAAE,wCAAM,CAAC,YAAY,CAAC,CAAC,EAAE,wCAAM,CAAC,IAAI,CAAC,CAAC;4DAClD,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,iBAAiB,KAAK,EAAE;gEACxB,SAAS,cAAc,CAAC;oEACtB,MAAM,KAAK,KAAK;oEAChB,UAAU,KAAK,QAAQ;gEACzB;gEACA,oBAAoB;4DACtB;sEAEA,cAAA,2BAAC,mBAAY;;;;;;;;;;sEAEf,2BAAC;4DACC,WAAW,CAAC,EAAE,wCAAM,CAAC,YAAY,CAAC,CAAC,EAAE,wCAAM,CAAC,MAAM,CAAC,CAAC;4DACpD,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,iBAAiB,KAAK,EAAE;4DAC1B;sEAEA,cAAA,2BAAC,qBAAc;;;;;;;;;;;;;;;;;;;;;;uCA5DhB,KAAK,EAAE;;;;;gCAkElB;;;;;;;;;;;sCAMJ,2BAAC,wBAAS;4BACR,OAAO,gBAAgB,WAAW;4BAClC,MAAM;4BACN,cAAc,CAAC;gCACb,oBAAoB;gCACpB,IAAI,CAAC,SAAS;oCACZ,iBAAiB;oCACjB,SAAS,WAAW;gCACtB;4BACF;4BACA,MAAM;4BACN,QAAO;4BACP,UAAU;4BACV,cAAa;4BACb,OAAO;4BACP,YAAY;gCACV,UAAU;gCACV,gBAAgB;gCAChB,cAAc;gCACd,UAAU;gCACV,aAAa;4BACf;4BACA,WAAW;gCACT,cAAc;oCACZ,YAAY,gBAAgB,SAAS;oCACrC,WAAW;gCACb;gCACA,mBAAmB;oCACjB,OAAO;wCACL,YAAY;wCACZ,aAAa;wCACb,WAAW;oCACb;oCACA,MAAM,8BAAgB,2BAAC,mBAAY;;;;+DAAM,2BAAC,mBAAY;;;;;gCACxD;gCACA,kBAAkB;oCAChB,OAAO;wCACL,aAAa;oCACf;gCACF;gCACA,SAAS;oCACP,oBAAoB;oCACpB,iBAAiB;oCACjB,SAAS,WAAW;gCACtB;4BACF;4BACA,UAAU;;8CAEV,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,2BAAC,WAAK;wCACJ,aAAY;wCACZ,MAAK;wCACL,OAAO;4CAAE,cAAc;wCAAE;;;;;;;;;;;8CAI7B,2BAAC,UAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,cAAc;oCACd,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAS;qCAAE;8CAE9C,cAAA,2BAAC,YAAM;wCACL,MAAK;wCACL,SAAS;4CACP;gDAAE,OAAO;gDAAG,OAAO;4CAAO;4CAC1B;gDAAE,OAAO;gDAAG,OAAO;4CAAO;4CAC1B;gDAAE,OAAO;gDAAG,OAAO;4CAAO;yCAC3B;wCACD,OAAO;4CAAE,cAAc;wCAAE;;;;;;;;;;;;;;;;;;;;;;;YAMzC;eA5cM;;oBAgBe,UAAI,CAAC;;;iBAhBpB;gBA8cN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCCvff;;;2BAAA;;;;gBAAA,WAAe;gBAAC,qBAAqB,CAAC,0BAA0B,CAAC;gBAAC,mBAAmB,CAAC,wBAAwB,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,OAAO,CAAC,YAAY,CAAC;gBAAC,YAAY,CAAC,iBAAiB,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,aAAa,CAAC,kBAAkB,CAAC;gBAAC,iBAAiB,CAAC,sBAAsB,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;gBAAC,aAAa,CAAC,kBAAkB,CAAC;gBAAC,iBAAiB,CAAC,sBAAsB,CAAC;gBAAC,kBAAkB,CAAC,uBAAuB,CAAC;gBAAC,uBAAuB,CAAC,4BAA4B,CAAC;gBAAC,oBAAoB,CAAC,yBAAyB,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,mBAAmB,CAAC,wBAAwB,CAAC;gBAAC,aAAa,CAAC,kBAAkB,CAAC;gBAAC,QAAQ,CAAC,aAAa,CAAC;gBAAC,iBAAiB,CAAC,sBAAsB,CAAC;gBAAC,QAAQ,CAAC,aAAa,CAAC;gBAAC,WAAW,CAAC,gBAAgB,CAAC;gBAAC,YAAY,CAAC,iBAAiB,CAAC;gBAAC,SAAS,CAAC,cAAc,CAAC;gBAAC,YAAY,CAAC,iBAAiB,CAAC;gBAAC,qBAAqB,CAAC,0BAA0B,CAAC;gBAAC,aAAa,CAAC,kBAAkB,CAAC;gBAAC,aAAa,CAAC,kBAAkB,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;gBAAC,aAAa,CAAC,kBAAkB,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;gBAAC,qBAAqB,CAAC,0BAA0B,CAAC;gBAAC,eAAe,CAAC,oBAAoB,CAAC;gBAAC,SAAS,CAAC,cAAc,CAAC;gBAAC,gBAAgB,CAAC,qBAAqB,CAAC;gBAAC,mBAAmB,CAAC,wBAAwB,CAAC;gBAAC,UAAU,CAAC,eAAe,CAAC;gBAAC,UAAU,CAAC,eAAe,CAAC;gBAAC,YAAY,CAAC,iBAAiB,CAAC;gBAAC,kBAAkB,CAAC,uBAAuB,CAAC;gBAAC,YAAY,CAAC,iBAAiB,CAAC;YAAA;;IFCj9C;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,gCAA+B;YAAC;SAAqB;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACrjB"}