import {
  CalendarOutlined,
  CheckOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  <PERSON>ert,
  Button,
  Card,
  Col,
  Flex,
  Form,
  Input,
  Progress,
  Row,
  Select,
  Spin,
  Tabs,
  Typography,
} from 'antd';
import { ModalForm } from '@ant-design/pro-components';
import React, { useEffect, useState } from 'react';
import { TodoService } from '@/services/todo';
import type { TodoResponse, TodoStatsResponse } from '@/types/api';
import styles from './TodoManagement.module.css';

const { Text } = Typography;
const { TabPane } = Tabs;

// 使用API类型定义，不需要重复定义接口
interface TodoManagementProps {
  onAddTodo?: (todo: TodoResponse) => void;
  onUpdateTodo?: (id: number, updatedTodo: Partial<TodoResponse>) => void;
  onDeleteTodo?: (id: number) => void;
}

const TodoManagement: React.FC<TodoManagementProps> = () => {
  // TODO数据状态管理
  const [personalTasks, setPersonalTasks] = useState<TodoResponse[]>([]);
  const [todoStats, setTodoStats] = useState<TodoStatsResponse>({
    highPriorityCount: 0,
    mediumPriorityCount: 0,
    lowPriorityCount: 0,
    totalCount: 0,
    completedCount: 0,
    completionPercentage: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 待办事项状态管理
  const [todoModalVisible, setTodoModalVisible] = useState(false);
  const [todoForm] = Form.useForm();
  const [editingTodoId, setEditingTodoId] = useState<number | null>(null);

  // 过滤器状态
  const [activeTab, setActiveTab] = useState<'all' | 'pending' | 'completed'>(
    'pending',
  );
  const [searchText, setSearchText] = useState('');

  // 获取TODO数据
  useEffect(() => {
    const fetchTodoData = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('TodoManagement: 开始获取TODO数据');

        // 分别获取TODO列表和统计数据，避免一个失败影响另一个
        const todosPromise = TodoService.getUserTodos().catch((error) => {
          console.error('获取TODO列表失败:', error);
          return [];
        });

        const statsPromise = TodoService.getTodoStats().catch((error) => {
          console.error('获取TODO统计失败:', error);
          return {
            highPriorityCount: 0,
            mediumPriorityCount: 0,
            lowPriorityCount: 0,
            totalCount: 0,
            completedCount: 0,
            completionPercentage: 0,
          };
        });

        const [todos, stats] = await Promise.all([todosPromise, statsPromise]);

        console.log('TodoManagement: 获取到TODO列表:', todos);
        console.log('TodoManagement: 获取到统计数据:', stats);

        setPersonalTasks(todos);
        setTodoStats(stats);
      } catch (error) {
        console.error('获取TODO数据时发生未知错误:', error);
        setError('获取TODO数据失败，请刷新页面重试');
      } finally {
        setLoading(false);
      }
    };

    fetchTodoData();
  }, []);

  // 根据激活的标签和搜索文本过滤任务
  const filteredPersonalTasks = (personalTasks || []).filter((task) => {
    // 根据标签过滤
    if (activeTab === 'pending' && task.status === 1) return false;
    if (activeTab === 'completed' && task.status === 0) return false;

    // 根据搜索文本过滤
    if (
      searchText &&
      !task.title.toLowerCase().includes(searchText.toLowerCase())
    ) {
      return false;
    }

    return true;
  });

  // 处理待办事项操作
  const handleToggleTodoStatus = async (id: number) => {
    try {
      const task = personalTasks.find((t) => t.id === id);
      if (!task) {
        return;
      }

      const newStatus = task.status === 0 ? 1 : 0;

      await TodoService.updateTodo(id, { status: newStatus });

      // 更新本地状态
      setPersonalTasks(
        personalTasks.map((task) =>
          task.id === id ? { ...task, status: newStatus } : task,
        ),
      );

      // 刷新统计数据
      try {
        const stats = await TodoService.getTodoStats();
        setTodoStats(stats);
      } catch (statsError) {
        // 统计数据刷新失败不影响主要操作
      }
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    }
  };

  const handleAddOrUpdateTodo = async (values: any) => {
    try {
      if (editingTodoId) {
        // 更新现有待办事项
        const updatedTodo = await TodoService.updateTodo(editingTodoId, {
          title: values.name,
          priority: values.priority,
        });

        setPersonalTasks(
          personalTasks.map((task) =>
            task.id === editingTodoId ? updatedTodo : task,
          ),
        );
      } else {
        // 添加新待办事项
        const newTodo = await TodoService.createTodo({
          title: values.name,
          priority: values.priority,
        });

        setPersonalTasks([newTodo, ...personalTasks]);
      }

      // 刷新统计数据
      try {
        const stats = await TodoService.getTodoStats();
        setTodoStats(stats);
      } catch (statsError) {
        // 统计数据刷新失败不影响主要操作
      }

      // 重置表单并关闭模态框
      setTodoModalVisible(false);
      setEditingTodoId(null);
      todoForm.resetFields();
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    }
  };

  const handleDeleteTodo = async (id: number) => {
    try {
      await TodoService.deleteTodo(id);
      setPersonalTasks(personalTasks.filter((task) => task.id !== id));

      // 刷新统计数据
      try {
        const stats = await TodoService.getTodoStats();
        setTodoStats(stats);
      } catch (statsError) {
        // 统计数据刷新失败不影响主要操作
      }
    } catch (error) {
      // 错误处理由响应拦截器统一处理
    }
  };

  return (
    <Card className={styles.todoContainer}>
      {/* 装饰元素 */}
      <div className={styles.decorativeElement}></div>
      <div className={styles.decorativeElement}></div>

      {/* 标题区域 */}
      <div style={{ position: 'relative', zIndex: 1, marginBottom: 24 }}>
        <Typography.Title level={3} className={styles.todoTitle}>
          待办事项管理
        </Typography.Title>
        <Typography.Text className={styles.todoSubtitle}>
          高效管理您的任务，提升工作效率
        </Typography.Text>
      </div>
      {/* 统计信息区域 */}
      <div className={styles.statsContainer} style={{ position: 'relative', zIndex: 1 }}>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{todoStats.totalCount}</div>
          <div className={styles.statLabel}>总任务</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{todoStats.completedCount}</div>
          <div className={styles.statLabel}>已完成</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{todoStats.highPriorityCount}</div>
          <div className={styles.statLabel}>高优先级</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{todoStats.mediumPriorityCount}</div>
          <div className={styles.statLabel}>中优先级</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statValue}>{todoStats.lowPriorityCount}</div>
          <div className={styles.statLabel}>低优先级</div>
        </div>
      </div>

      {/* 进度条区域 */}
      <div className={styles.progressContainer} style={{ position: 'relative', zIndex: 1 }}>
        <div className={styles.progressLabel}>
          任务完成进度 {todoStats.completedCount}/{todoStats.totalCount}
        </div>
        <Progress
          percent={todoStats.completionPercentage}
          strokeColor={{
            '0%': '#4facfe',
            '100%': '#00f2fe',
          }}
          trailColor="rgba(255, 255, 255, 0.2)"
          showInfo={false}
          strokeWidth={8}
        />
        <div style={{ textAlign: 'right', marginTop: 8 }}>
          <span style={{ color: 'white', fontSize: 16, fontWeight: 600 }}>
            {todoStats.completionPercentage}%
          </span>
        </div>
      </div>

      {/* 操作区域 */}
      <div className={styles.actionContainer} style={{ position: 'relative', zIndex: 1 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={16} md={18} lg={20}>
            <Input.Search
              placeholder="搜索任务..."
              allowClear
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className={styles.searchInput}
              size="large"
            />
          </Col>
          <Col xs={24} sm={8} md={6} lg={4}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              className={styles.addButton}
              size="large"
              block
              onClick={() => {
                setEditingTodoId(null);
                todoForm.resetFields();
                setTodoModalVisible(true);
              }}
            >
              添加新任务
            </Button>
          </Col>
        </Row>
      </div>

      {/* 标签页 */}
      <Tabs
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as 'all' | 'pending' | 'completed')}
        size="large"
        className={styles.todoTabs}
        style={{ position: 'relative', zIndex: 1, marginBottom: 16 }}
      >
        <TabPane tab="全部任务" key="all" />
        <TabPane tab="待处理" key="pending" />
        <TabPane tab="已完成" key="completed" />
      </Tabs>

      {/* 任务列表 */}
      <div className={styles.todoListContainer} style={{ position: 'relative', zIndex: 1 }}>
        {error ? (
          <Alert
            message="TODO数据加载失败"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: 16 }}
          />
        ) : (
          <Spin spinning={loading}>
            {filteredPersonalTasks.length === 0 ? (
              <div style={{ textAlign: 'center', padding: '40px 0', color: 'rgba(255, 255, 255, 0.6)' }}>
                <Typography.Text style={{ color: 'rgba(255, 255, 255, 0.6)' }}>
                  暂无任务数据
                </Typography.Text>
              </div>
            ) : (
              filteredPersonalTasks.map((item) => {
                const priorityClass = item.priority === 3 ? 'highPriority' :
                                    item.priority === 2 ? 'mediumPriority' : 'lowPriority';
                const statusClass = item.status === 1 ? 'completed' : 'pending';

                return (
                  <div
                    key={item.id}
                    className={`${styles.todoItem} ${styles[priorityClass]} ${item.status === 1 ? styles.completed : ''}`}
                  >
                    <Flex align="center" gap={16} style={{ width: '100%' }}>
                      {/* 状态指示器 */}
                      <div
                        className={`${styles.statusIndicator} ${styles[statusClass]} ${styles[priorityClass]}`}
                        onClick={() => handleToggleTodoStatus(item.id, item.status === 1 ? 0 : 1)}
                      >
                        {item.status === 1 && (
                          <CheckOutlined style={{ color: '#fff', fontSize: 12 }} />
                        )}
                      </div>

                      {/* 任务内容 */}
                      <div className={styles.todoContent}>
                        <div className={styles.todoTitle} style={{
                          textDecoration: item.status === 1 ? 'line-through' : 'none',
                          color: item.status === 1 ? '#8c8c8c' : '#262626',
                        }}>
                          {item.title}
                        </div>
                        {item.description && (
                          <div className={styles.todoDescription}>
                            {item.description}
                          </div>
                        )}

                        <div className={styles.todoMeta}>
                          <CalendarOutlined />
                          <span>创建于 {new Date(item.createdAt).toLocaleDateString('zh-CN')}</span>
                          <span className={`${styles.priorityBadge} ${styles[priorityClass.replace('Priority', '')]}`}>
                            {item.priority === 3 ? '高优先级' : item.priority === 2 ? '中优先级' : '低优先级'}
                          </span>
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className={styles.todoActions}>
                        <button
                          className={`${styles.actionButton} ${styles.edit}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingTodoId(item.id);
                            todoForm.setFieldsValue({
                              name: item.title,
                              priority: item.priority,
                            });
                            setTodoModalVisible(true);
                          }}
                        >
                          <EditOutlined />
                        </button>
                        <button
                          className={`${styles.actionButton} ${styles.delete}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTodo(item.id);
                          }}
                        >
                          <DeleteOutlined />
                        </button>
                      </div>
                    </Flex>
                  </div>
                );
              })
            )}
          </Spin>
        )}
      </div>
      {/* 待办事项表单模态框 */}
          <ModalForm
            title={editingTodoId ? '编辑待办事项' : '新增待办事项'}
            open={todoModalVisible}
            onOpenChange={(visible) => {
              setTodoModalVisible(visible);
              if (!visible) {
                setEditingTodoId(null);
                todoForm.resetFields();
              }
            }}
            form={todoForm}
            layout="vertical"
            onFinish={handleAddOrUpdateTodo}
            autoComplete="off"
            width={500}
            modalProps={{
              centered: true,
              destroyOnClose: true,
              maskClosable: true,
              keyboard: true,
              forceRender: false,
            }}
            submitter={{
              searchConfig: {
                submitText: editingTodoId ? '更新任务' : '创建任务',
                resetText: '取消',
              },
              submitButtonProps: {
                style: {
                  background: '#1890ff',
                  borderColor: '#1890ff',
                  boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',
                },
                icon: editingTodoId ? <EditOutlined /> : <PlusOutlined />,
              },
              resetButtonProps: {
                style: {
                  borderColor: '#d9d9d9',
                },
              },
              onReset: () => {
                setTodoModalVisible(false);
                setEditingTodoId(null);
                todoForm.resetFields();
              },
            }}
            preserve={false}
          >
            <Form.Item
              name="name"
              label="任务名称"
              rules={[{ required: true, message: '请输入任务名称' }]}
            >
              <Input
                placeholder="请输入任务名称"
                size="large"
                style={{ borderRadius: 6 }}
              />
            </Form.Item>

            <Form.Item
              name="priority"
              label="优先级"
              initialValue={2}
              rules={[{ required: true, message: '请选择优先级' }]}
            >
              <Select
                size="large"
                options={[
                  { value: 3, label: '高优先级' },
                  { value: 2, label: '中优先级' },
                  { value: 1, label: '低优先级' },
                ]}
                style={{ borderRadius: 6 }}
              />
            </Form.Item>
          </ModalForm>
    </Card>
  );
};

export default TodoManagement;
